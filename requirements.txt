# CrewAI OSINT Analyst Dependencies

# Core CrewAI Framework
crewai>=0.28.8
crewai-tools>=0.1.6

# Web Scraping and Browser Automation
browser-use>=0.1.0
crawl4ai>=0.2.77
playwright>=1.40.0
selenium>=4.15.0

# HTTP and API clients
requests>=2.31.0
httpx>=0.25.0
aiohttp>=3.9.0

# Data Processing and Analysis
pandas>=2.1.0
numpy>=1.24.0
beautifulsoup4>=4.12.0
lxml>=4.9.0

# Language Models and AI
openai>=1.3.0
anthropic>=0.7.0
langchain>=0.1.0
langchain-community>=0.0.10

# Configuration and Environment
python-dotenv>=1.0.0
pydantic>=2.5.0
pyyaml>=6.0

# Logging and Monitoring
loguru>=0.7.0
rich>=13.7.0

# Date and Time Handling
python-dateutil>=2.8.0
pytz>=2023.3

# JSON and Data Serialization
orjson>=3.9.0

# Optional: For advanced features
# stagehand (if available via pip or git)
# Note: stagehand might need to be installed from source

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.7.0
