#!/usr/bin/env python3
"""
Interactive Demo for CrewAI OSINT Framework
Allows users to test different scenarios interactively
"""

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from workflows import GeoOSINTWorkflow, CTIOSINTWorkflow
from config.settings import validate_api_keys
from loguru import logger
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.table import Table


def setup_logging():
    """Setup logging configuration"""
    logger.remove()
    logger.add(
        "logs/interactive_demo.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="10 MB"
    )


def display_welcome(console):
    """Display welcome message"""
    welcome_text = """
🔍 CrewAI OSINT Framework - Interactive Demo

This demo allows you to test the OSINT analysis capabilities of both:
• Geopolitical OSINT Analyst
• Cyber Threat Intelligence OSINT Analyst

Choose from predefined scenarios or create custom analysis tasks.
    """
    
    console.print(Panel(welcome_text, title="Welcome", border_style="blue"))


def check_api_status(console):
    """Check and display API key status"""
    api_status = validate_api_keys()
    
    table = Table(title="API Key Status")
    table.add_column("Service", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Notes", style="yellow")
    
    for service, available in api_status.items():
        status = "✅ Available" if available else "❌ Missing"
        notes = "Ready to use" if available else "Some features may be limited"
        table.add_row(service.upper(), status, notes)
    
    console.print(table)
    
    if not any(api_status.values()):
        console.print("\n⚠️  [yellow]No API keys configured. Please set up API keys in .env file for full functionality.[/yellow]")
    
    return api_status


def geo_osint_menu(console, workflow):
    """Geopolitical OSINT analysis menu"""
    console.print("\n[bold blue]Geopolitical OSINT Analysis[/bold blue]")
    
    options = {
        "1": "Analyze regional situation (custom)",
        "2": "South Sudan political unrest",
        "3": "US-China diplomatic relations",
        "4": "Ukraine conflict analysis",
        "5": "Custom political crisis analysis",
        "6": "Election integrity assessment"
    }
    
    for key, value in options.items():
        console.print(f"{key}. {value}")
    
    choice = Prompt.ask("Select an option", choices=list(options.keys()))
    
    if choice == "1":
        region = Prompt.ask("Enter region to analyze")
        timeframe = Prompt.ask("Enter timeframe", default="7d")
        return workflow.analyze_regional_situation(region, timeframe)
    
    elif choice == "2":
        return workflow.analyze_regional_situation(
            region="South Sudan",
            timeframe="48h",
            focus_areas=["political_developments", "security_situation"]
        )
    
    elif choice == "3":
        return workflow.monitor_diplomatic_relations(
            country1="United States",
            country2="China"
        )
    
    elif choice == "4":
        return workflow.analyze_regional_situation(
            region="Ukraine",
            timeframe="7d"
        )
    
    elif choice == "5":
        country = Prompt.ask("Enter country")
        crisis = Prompt.ask("Describe the crisis")
        return workflow.track_political_crisis(country, crisis)
    
    elif choice == "6":
        country = Prompt.ask("Enter country")
        date = Prompt.ask("Enter election date")
        return workflow.assess_election_integrity(country, date)


def cti_osint_menu(console, workflow):
    """Cyber Threat Intelligence OSINT analysis menu"""
    console.print("\n[bold red]Cyber Threat Intelligence OSINT Analysis[/bold red]")
    
    options = {
        "1": "Analyze threat actor (custom)",
        "2": "APT29 analysis",
        "3": "Ransomware landscape monitoring",
        "4": "LockBit ransomware analysis",
        "5": "Vulnerability threat assessment",
        "6": "Malware campaign investigation"
    }
    
    for key, value in options.items():
        console.print(f"{key}. {value}")
    
    choice = Prompt.ask("Select an option", choices=list(options.keys()))
    
    if choice == "1":
        actor = Prompt.ask("Enter threat actor name")
        return workflow.analyze_threat_actor(actor)
    
    elif choice == "2":
        return workflow.analyze_threat_actor("APT29")
    
    elif choice == "3":
        return workflow.monitor_ransomware_activity(timeframe="30d")
    
    elif choice == "4":
        return workflow.monitor_ransomware_activity("LockBit", "30d")
    
    elif choice == "5":
        cve = Prompt.ask("Enter CVE ID", default="CVE-2024-21412")
        return workflow.assess_vulnerability_threat(cve)
    
    elif choice == "6":
        malware = Prompt.ask("Enter malware family name")
        timeframe = Prompt.ask("Enter timeframe", default="30d")
        return workflow.investigate_malware_campaign(malware, timeframe)


def main():
    """Main interactive demo function"""
    setup_logging()
    console = Console()
    
    display_welcome(console)
    
    # Check API status
    api_status = check_api_status(console)
    
    if not any(api_status.values()):
        if not Confirm.ask("\nContinue without API keys? (Limited functionality)"):
            console.print("Demo cancelled. Please configure API keys and try again.")
            return
    
    # Initialize workflows
    geo_workflow = GeoOSINTWorkflow()
    cti_workflow = CTIOSINTWorkflow()
    
    while True:
        console.print("\n" + "="*60)
        console.print("[bold]Main Menu[/bold]")
        console.print("1. Geopolitical OSINT Analysis")
        console.print("2. Cyber Threat Intelligence OSINT Analysis")
        console.print("3. View previous reports")
        console.print("4. Exit")
        
        choice = Prompt.ask("Select an option", choices=["1", "2", "3", "4"])
        
        if choice == "1":
            try:
                result = geo_osint_menu(console, geo_workflow)
                if result.get('success'):
                    console.print(f"\n✅ [green]Analysis completed successfully![/green]")
                    console.print(f"Execution time: {result.get('execution_time', 0):.2f} seconds")
                else:
                    console.print(f"\n❌ [red]Analysis failed: {result.get('error', 'Unknown error')}[/red]")
            except Exception as e:
                console.print(f"\n❌ [red]Error: {e}[/red]")
        
        elif choice == "2":
            try:
                result = cti_osint_menu(console, cti_workflow)
                if result.get('success'):
                    console.print(f"\n✅ [green]Analysis completed successfully![/green]")
                    console.print(f"Execution time: {result.get('execution_time', 0):.2f} seconds")
                else:
                    console.print(f"\n❌ [red]Analysis failed: {result.get('error', 'Unknown error')}[/red]")
            except Exception as e:
                console.print(f"\n❌ [red]Error: {e}[/red]")
        
        elif choice == "3":
            # Show previous reports
            geo_reports = geo_workflow.list_previous_reports()
            cti_reports = cti_workflow.list_previous_reports()
            
            if geo_reports or cti_reports:
                console.print(f"\n[bold]Previous Reports[/bold]")
                console.print(f"Geopolitical reports: {len(geo_reports)}")
                console.print(f"CTI reports: {len(cti_reports)}")
                console.print("Check the output/reports directory for full reports.")
            else:
                console.print("\n[yellow]No previous reports found.[/yellow]")
        
        elif choice == "4":
            console.print("\n[green]Thank you for using the CrewAI OSINT Framework![/green]")
            break
        
        if choice in ["1", "2"]:
            console.print("\n📁 Reports saved to: output/reports/")
            if not Confirm.ask("Run another analysis?"):
                break


if __name__ == "__main__":
    main()
