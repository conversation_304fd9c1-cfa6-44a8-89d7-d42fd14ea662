#!/usr/bin/env python3
"""
Cyber Threat Intelligence OSINT Analysis Example
Demonstrates the CTI OSINT workflow with sample scenarios
"""

import asyncio
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from workflows import CTIOSINTWorkflow
from config.settings import validate_api_keys
from loguru import logger


def setup_logging():
    """Setup logging configuration"""
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    logger.add(
        "logs/cti_osint_example.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="10 MB"
    )


def main():
    """Run cyber threat intelligence OSINT analysis examples"""
    setup_logging()
    logger.info("Starting Cyber Threat Intelligence OSINT Analysis Examples")
    
    # Validate API keys
    api_status = validate_api_keys()
    logger.info(f"API Key Status: {api_status}")
    
    if not any(api_status.values()):
        logger.warning("No API keys configured. Some tools may not work.")
        logger.info("Please set API keys in .env file for full functionality")
    
    # Initialize workflow
    workflow = CTIOSINTWorkflow()
    
    # Example scenarios
    scenarios = [
        {
            "name": "APT29 Threat Actor Analysis",
            "description": "Analyze recent activities and TTPs of APT29 (Cozy Bear)",
            "function": lambda: workflow.analyze_threat_actor(
                actor_name="APT29",
                focus_areas=["recent_campaigns", "ttps_analysis", "infrastructure", "attribution_assessment"]
            )
        },
        {
            "name": "Ransomware Landscape Monitoring",
            "description": "Monitor recent ransomware activities and trends",
            "function": lambda: workflow.monitor_ransomware_activity(
                timeframe="30d"
            )
        },
        {
            "name": "LockBit Ransomware Analysis",
            "description": "Investigate LockBit ransomware group activities",
            "function": lambda: workflow.monitor_ransomware_activity(
                ransomware_group="LockBit",
                timeframe="30d"
            )
        },
        {
            "name": "Critical Vulnerability Assessment",
            "description": "Assess threat landscape for a recent critical vulnerability",
            "function": lambda: workflow.assess_vulnerability_threat(
                cve_id="CVE-2024-21412"  # Example CVE
            )
        },
        {
            "name": "Emotet Malware Campaign",
            "description": "Investigate recent Emotet malware campaign activities",
            "function": lambda: workflow.investigate_malware_campaign(
                malware_family="Emotet",
                timeframe="60d"
            )
        }
    ]
    
    # Run scenarios
    for i, scenario in enumerate(scenarios, 1):
        logger.info(f"\n{'='*60}")
        logger.info(f"Running Scenario {i}: {scenario['name']}")
        logger.info(f"Description: {scenario['description']}")
        logger.info(f"{'='*60}")
        
        try:
            result = scenario['function']()
            
            if result.get('success'):
                logger.success(f"✅ Scenario {i} completed successfully")
                logger.info(f"Execution time: {result.get('execution_time', 0):.2f} seconds")
                logger.info(f"Tasks completed: {result.get('tasks_completed', 0)}")
            else:
                logger.error(f"❌ Scenario {i} failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.exception(f"❌ Scenario {i} failed with exception: {e}")
        
        logger.info(f"Scenario {i} completed\n")
    
    logger.info("All cyber threat intelligence OSINT examples completed")
    logger.info("Check the output/reports directory for generated reports")


if __name__ == "__main__":
    main()
