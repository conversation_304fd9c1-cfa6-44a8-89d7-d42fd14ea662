#!/usr/bin/env python3
"""
Geopolitical OSINT Analysis Example
Demonstrates the GeoOSINT workflow with sample scenarios
"""

import asyncio
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from workflows import GeoOSINTWorkflow
from config.settings import validate_api_keys
from loguru import logger


def setup_logging():
    """Setup logging configuration"""
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    logger.add(
        "logs/geo_osint_example.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="10 MB"
    )


def main():
    """Run geopolitical OSINT analysis examples"""
    setup_logging()
    logger.info("Starting Geopolitical OSINT Analysis Examples")
    
    # Validate API keys
    api_status = validate_api_keys()
    logger.info(f"API Key Status: {api_status}")
    
    if not any(api_status.values()):
        logger.warning("No API keys configured. Some tools may not work.")
        logger.info("Please set API keys in .env file for full functionality")
    
    # Initialize workflow
    workflow = GeoOSINTWorkflow()
    
    # Example scenarios
    scenarios = [
        {
            "name": "South Sudan Political Unrest Analysis",
            "description": "Analyze recent political developments in South Sudan",
            "function": lambda: workflow.analyze_regional_situation(
                region="South Sudan",
                timeframe="48h",
                focus_areas=["political_developments", "security_situation", "humanitarian_impact"]
            )
        },
        {
            "name": "US-China Diplomatic Relations",
            "description": "Monitor current state of US-China diplomatic relations",
            "function": lambda: workflow.monitor_diplomatic_relations(
                country1="United States",
                country2="China",
                context="Trade tensions and strategic competition"
            )
        },
        {
            "name": "Ukraine Conflict Analysis",
            "description": "Analyze recent developments in Ukraine conflict",
            "function": lambda: workflow.analyze_regional_situation(
                region="Ukraine",
                timeframe="7d",
                focus_areas=["military_developments", "international_support", "diplomatic_efforts"]
            )
        }
    ]
    
    # Run scenarios
    for i, scenario in enumerate(scenarios, 1):
        logger.info(f"\n{'='*60}")
        logger.info(f"Running Scenario {i}: {scenario['name']}")
        logger.info(f"Description: {scenario['description']}")
        logger.info(f"{'='*60}")
        
        try:
            result = scenario['function']()
            
            if result.get('success'):
                logger.success(f"✅ Scenario {i} completed successfully")
                logger.info(f"Execution time: {result.get('execution_time', 0):.2f} seconds")
                logger.info(f"Tasks completed: {result.get('tasks_completed', 0)}")
            else:
                logger.error(f"❌ Scenario {i} failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.exception(f"❌ Scenario {i} failed with exception: {e}")
        
        logger.info(f"Scenario {i} completed\n")
    
    logger.info("All geopolitical OSINT examples completed")
    logger.info("Check the output/reports directory for generated reports")


if __name__ == "__main__":
    main()
