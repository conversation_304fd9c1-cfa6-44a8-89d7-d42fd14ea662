# 🛠️ Setup Guide

This guide provides detailed setup instructions for the CrewAI OSINT Framework.

## 📋 System Requirements

### Minimum Requirements
- **Python**: 3.8 or higher
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space
- **Internet**: Stable connection for API calls and web crawling

### Supported Platforms
- **macOS**: 10.14+ (Intel/Apple Silicon)
- **Linux**: Ubuntu 18.04+, CentOS 7+, Debian 10+
- **Windows**: 10/11 (with WSL recommended)

## 🔧 Installation Steps

### 1. Environment Setup

#### Option A: Using Virtual Environment (Recommended)
```bash
# Create virtual environment
python3 -m venv crewai-osint-env

# Activate virtual environment
# On macOS/Linux:
source crewai-osint-env/bin/activate
# On Windows:
crewai-osint-env\Scripts\activate

# Verify activation
which python  # Should show path to virtual environment
```

#### Option B: Using Conda
```bash
# Create conda environment
conda create -n crewai-osint python=3.11
conda activate crewai-osint
```

### 2. Install Dependencies

```bash
# Upgrade pip
pip install --upgrade pip

# Install requirements
pip install -r requirements.txt

# Install Playwright browsers (required for browser automation)
playwright install

# Verify installation
python -c "import crewai; print('CrewAI installed successfully')"
```

### 3. Configuration

#### Create Environment File
```bash
# Copy example configuration
cp .env.example .env

# Edit configuration (use your preferred editor)
nano .env
# or
vim .env
# or
code .env
```

#### Configure API Keys

Edit the `.env` file with your API keys:

```env
# Essential for full functionality
OPENAI_API_KEY=sk-your-openai-key-here
SERPER_API_KEY=your-serper-key-here

# Optional alternatives
ANTHROPIC_API_KEY=your-anthropic-key-here

# Model settings
DEFAULT_LLM_MODEL=gpt-4-turbo-preview
LLM_TEMPERATURE=0.1
MAX_TOKENS=4000

# Browser settings
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30

# Crawling settings
CRAWL_DELAY=1.0
MAX_PAGES_PER_DOMAIN=10

# Output settings
OUTPUT_FORMAT=json
SAVE_RAW_DATA=true
LOG_LEVEL=INFO
```

## 🔑 API Key Setup

### OpenAI API Key
1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Sign up or log in to your account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key to your `.env` file

**Pricing**: Pay-per-use, typically $0.01-0.06 per 1K tokens

### Serper API Key
1. Visit [Serper.dev](https://serper.dev/)
2. Sign up for an account
3. Get your API key from the dashboard
4. Copy the key to your `.env` file

**Pricing**: Free tier available (2,500 searches), paid plans from $5/month

### Anthropic API Key (Optional)
1. Visit [Anthropic Console](https://console.anthropic.com/)
2. Sign up or log in
3. Generate an API key
4. Copy the key to your `.env` file

**Pricing**: Pay-per-use, competitive with OpenAI

## ✅ Verification

### Test Installation
```bash
# Test basic functionality
python -c "
from config.settings import validate_api_keys
print('API Status:', validate_api_keys())
"

# Test tool imports
python -c "
from tools import SerperWrapper, Crawl4AIWrapper, BrowserUseWrapper
print('Tools imported successfully')
"

# Test agent imports
python -c "
from agents import GeoOSINTAnalyst, CyberThreatOSINTAnalyst
print('Agents imported successfully')
"
```

### Run Test Examples
```bash
# Quick functionality test
python examples/interactive_demo.py

# Run specific examples
python examples/geo_osint_example.py
python examples/cti_osint_example.py
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Import Errors
```bash
# Error: ModuleNotFoundError
# Solution: Ensure virtual environment is activated
source crewai-osint-env/bin/activate
pip install -r requirements.txt
```

#### 2. Playwright Browser Issues
```bash
# Error: Browser not found
# Solution: Install Playwright browsers
playwright install

# For specific browsers only:
playwright install chromium
```

#### 3. API Key Issues
```bash
# Error: API key not found
# Solution: Check .env file exists and has correct format
ls -la .env
cat .env | grep API_KEY
```

#### 4. Permission Issues (Linux/macOS)
```bash
# Error: Permission denied
# Solution: Fix permissions
chmod +x examples/*.py
```

#### 5. Memory Issues
```bash
# Error: Out of memory
# Solution: Reduce concurrent operations
# Edit .env:
MAX_PAGES_PER_DOMAIN=5
BROWSER_TIMEOUT=15
```

### Debug Mode

Enable debug logging:
```bash
# Edit .env
LOG_LEVEL=DEBUG

# Or set environment variable
export LOG_LEVEL=DEBUG
python examples/interactive_demo.py
```

### Log Files

Check log files for detailed error information:
```bash
# View recent logs
tail -f logs/osint_agent.log

# Search for errors
grep -i error logs/*.log
```

## 🔄 Updates

### Updating Dependencies
```bash
# Activate environment
source crewai-osint-env/bin/activate

# Update packages
pip install --upgrade -r requirements.txt

# Update Playwright
playwright install
```

### Updating Configuration
```bash
# Backup current config
cp .env .env.backup

# Update from new example
cp .env.example .env.new
# Merge your settings manually
```

## 🚀 Performance Optimization

### For Better Performance
```bash
# Install optional performance packages
pip install uvloop  # Faster async event loop (Linux/macOS)
pip install orjson  # Faster JSON processing
```

### Configuration Tuning
```env
# Faster but less detailed analysis
LLM_TEMPERATURE=0.0
MAX_TOKENS=2000

# More concurrent operations (if you have good hardware)
MAX_PAGES_PER_DOMAIN=20
CRAWL_DELAY=0.5
```

## 🔒 Security Considerations

### API Key Security
- Never commit `.env` files to version control
- Use environment variables in production
- Rotate API keys regularly
- Monitor API usage for anomalies

### Network Security
- Use VPN when conducting sensitive OSINT
- Be aware of rate limiting and IP blocking
- Respect robots.txt and terms of service

## 📞 Support

If you encounter issues:

1. Check this setup guide
2. Review the troubleshooting section
3. Check the logs for detailed error messages
4. Search existing issues in the repository
5. Create a new issue with:
   - Your operating system
   - Python version
   - Error messages
   - Steps to reproduce

## 🎯 Next Steps

After successful setup:

1. **Read the main README.md** for usage examples
2. **Try the interactive demo** to familiarize yourself with the interface
3. **Run example scenarios** to see the framework in action
4. **Customize workflows** for your specific use cases
5. **Explore the API documentation** for advanced usage

---

**✅ Setup Complete!** You're now ready to use the CrewAI OSINT Framework.
