"""
Crawl4AI wrapper for intelligent web crawling and content extraction
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urljoin, urlparse
import re

from crawl4ai import AsyncWeb<PERSON>rawler
from crawl4ai.extraction_strategy import LLMExtractionStrategy, CosineStrategy
from pydantic import Field

from .base_tool import BaseOSINTTool, OSINTToolResult
from ..config.settings import get_settings


class Crawl4AIWrapper(BaseOSINTTool):
    """Wrapper for Crawl4AI intelligent web crawling"""
    
    max_pages: int = Field(default=5, description="Maximum pages to crawl per domain")
    delay: float = Field(default=1.0, description="Delay between requests in seconds")
    timeout: int = Field(default=30, description="Request timeout in seconds")
    
    def __init__(self, **kwargs):
        settings = get_settings()
        
        super().__init__(
            name="Crawl4AI",
            description="Intelligent web crawling and content extraction. Can extract structured data, follow links, and analyze web content for OSINT purposes.",
            max_pages=kwargs.get("max_pages", settings.max_pages_per_domain),
            delay=kwargs.get("delay", settings.crawl_delay),
            timeout=kwargs.get("timeout", settings.browser_timeout),
            **kwargs
        )
    
    async def _execute_async(self, url: str, extraction_type: str = "content", 
                           follow_links: bool = False, **kwargs) -> OSINTToolResult:
        """
        Execute web crawling with Crawl4AI
        
        Args:
            url: Target URL to crawl
            extraction_type: Type of extraction (content, links, structured, llm)
            follow_links: Whether to follow internal links
        """
        try:
            self.logger.info(f"Starting Crawl4AI extraction: {url}")
            
            async with AsyncWebCrawler(
                headless=True,
                browser_type="chromium",
                verbose=False
            ) as crawler:
                
                if extraction_type == "llm" and "extraction_prompt" in kwargs:
                    # Use LLM extraction strategy
                    extraction_strategy = LLMExtractionStrategy(
                        provider="openai",
                        api_token=get_settings().openai_api_key,
                        instruction=kwargs["extraction_prompt"]
                    )
                    
                    result = await crawler.arun(
                        url=url,
                        extraction_strategy=extraction_strategy,
                        bypass_cache=True
                    )
                    
                    extracted_data = {
                        "extracted_content": result.extracted_content,
                        "markdown": result.markdown,
                        "metadata": result.metadata
                    }
                
                elif extraction_type == "structured":
                    # Extract structured data using CSS selectors or patterns
                    css_selectors = kwargs.get("css_selectors", {})
                    
                    result = await crawler.arun(
                        url=url,
                        css_selector=css_selectors.get("main", "body"),
                        bypass_cache=True
                    )
                    
                    extracted_data = await self._extract_structured_data(result, css_selectors)
                
                elif extraction_type == "links":
                    # Extract and analyze links
                    result = await crawler.arun(url=url, bypass_cache=True)
                    extracted_data = self._extract_links_data(result, url)
                
                else:
                    # Default content extraction
                    result = await crawler.arun(url=url, bypass_cache=True)
                    extracted_data = {
                        "title": result.metadata.get("title", ""),
                        "description": result.metadata.get("description", ""),
                        "content": result.cleaned_html,
                        "markdown": result.markdown,
                        "word_count": len(result.markdown.split()) if result.markdown else 0,
                        "links_found": len(result.links.get("internal", [])) + len(result.links.get("external", [])),
                        "images_found": len(result.media.get("images", [])) if result.media else 0
                    }
                
                # Follow internal links if requested
                if follow_links and extraction_type != "links":
                    internal_links = result.links.get("internal", [])[:self.max_pages-1]
                    if internal_links:
                        extracted_data["followed_links"] = await self._crawl_internal_links(
                            crawler, internal_links, url
                        )
                
                return OSINTToolResult(
                    success=True,
                    data=extracted_data,
                    source_url=url,
                    metadata={
                        "extraction_type": extraction_type,
                        "follow_links": follow_links,
                        "status_code": result.status_code,
                        "response_headers": dict(result.response_headers) if result.response_headers else {},
                        "crawl_time": result.metadata.get("crawl_time")
                    },
                    timestamp=datetime.now().isoformat()
                )
                
        except Exception as e:
            error_msg = f"Crawl4AI extraction failed: {str(e)}"
            self.logger.error(error_msg)
            return OSINTToolResult(success=False, error=error_msg)
    
    async def _extract_structured_data(self, result, css_selectors: Dict[str, str]) -> Dict[str, Any]:
        """Extract structured data using CSS selectors"""
        from bs4 import BeautifulSoup
        
        soup = BeautifulSoup(result.html, 'html.parser')
        structured_data = {}
        
        for field_name, selector in css_selectors.items():
            try:
                elements = soup.select(selector)
                if elements:
                    if len(elements) == 1:
                        structured_data[field_name] = self._safe_extract_text(elements[0])
                    else:
                        structured_data[field_name] = [
                            self._safe_extract_text(elem) for elem in elements
                        ]
                else:
                    structured_data[field_name] = None
            except Exception as e:
                self.logger.warning(f"Failed to extract {field_name}: {e}")
                structured_data[field_name] = None
        
        # Add basic metadata
        structured_data.update({
            "title": result.metadata.get("title", ""),
            "description": result.metadata.get("description", ""),
            "word_count": len(result.markdown.split()) if result.markdown else 0
        })
        
        return structured_data
    
    def _extract_links_data(self, result, base_url: str) -> Dict[str, Any]:
        """Extract and categorize links from the page"""
        links_data = {
            "internal_links": [],
            "external_links": [],
            "social_media_links": [],
            "email_addresses": [],
            "phone_numbers": []
        }
        
        # Process internal and external links
        if result.links:
            for link in result.links.get("internal", []):
                links_data["internal_links"].append({
                    "url": urljoin(base_url, link),
                    "text": link
                })
            
            for link in result.links.get("external", []):
                domain = urlparse(link).netloc.lower()
                if any(social in domain for social in ['twitter', 'facebook', 'linkedin', 'instagram', 'telegram', 'youtube']):
                    links_data["social_media_links"].append(link)
                else:
                    links_data["external_links"].append(link)
        
        # Extract emails and phone numbers from content
        if result.markdown:
            # Email pattern
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, result.markdown)
            links_data["email_addresses"] = list(set(emails))
            
            # Phone pattern (basic international format)
            phone_pattern = r'[\+]?[1-9]?[0-9]{7,15}'
            phones = re.findall(phone_pattern, result.markdown)
            links_data["phone_numbers"] = list(set(phones))
        
        return links_data
    
    async def _crawl_internal_links(self, crawler, links: List[str], base_url: str) -> List[Dict[str, Any]]:
        """Crawl internal links and extract basic information"""
        followed_data = []
        
        for i, link in enumerate(links[:self.max_pages-1]):
            try:
                await asyncio.sleep(self.delay)  # Rate limiting
                
                full_url = urljoin(base_url, link)
                result = await crawler.arun(url=full_url, bypass_cache=True)
                
                followed_data.append({
                    "url": full_url,
                    "title": result.metadata.get("title", ""),
                    "description": result.metadata.get("description", ""),
                    "word_count": len(result.markdown.split()) if result.markdown else 0,
                    "status_code": result.status_code
                })
                
            except Exception as e:
                self.logger.warning(f"Failed to crawl {link}: {e}")
                followed_data.append({
                    "url": urljoin(base_url, link),
                    "error": str(e)
                })
        
        return followed_data
    
    # Convenience methods for specific extraction types
    async def extract_content(self, url: str, **kwargs) -> OSINTToolResult:
        """Extract main content from a webpage"""
        return await self._execute_async(url, "content", **kwargs)
    
    async def extract_links(self, url: str, **kwargs) -> OSINTToolResult:
        """Extract and categorize all links from a webpage"""
        return await self._execute_async(url, "links", **kwargs)
    
    async def extract_with_llm(self, url: str, prompt: str, **kwargs) -> OSINTToolResult:
        """Extract specific information using LLM with custom prompt"""
        return await self._execute_async(url, "llm", extraction_prompt=prompt, **kwargs)
