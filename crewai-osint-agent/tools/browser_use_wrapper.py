"""
Browser-Use wrapper for advanced browser automation and interaction
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
import json

from browser_use import Agent
from pydantic import Field

from .base_tool import BaseOSINTTool, OSINTToolResult
from ..config.settings import get_settings


class BrowserUseWrapper(BaseOSINTTool):
    """Wrapper for Browser-Use agent automation"""
    
    headless: bool = Field(default=True, description="Run browser in headless mode")
    timeout: int = Field(default=60, description="Browser operation timeout")
    max_steps: int = Field(default=20, description="Maximum automation steps")
    
    def __init__(self, **kwargs):
        settings = get_settings()
        
        super().__init__(
            name="BrowserUse",
            description="Advanced browser automation for complex web interactions. Can navigate websites, fill forms, click elements, and extract data through natural language instructions.",
            headless=kwargs.get("headless", settings.browser_headless),
            timeout=kwargs.get("timeout", settings.browser_timeout),
            **kwargs
        )
    
    async def _execute_async(self, task: str, starting_url: Optional[str] = None, 
                           context: Optional[Dict[str, Any]] = None, **kwargs) -> OSINTToolResult:
        """
        Execute browser automation task
        
        Args:
            task: Natural language description of the task to perform
            starting_url: Optional starting URL
            context: Additional context for the task
        """
        try:
            self.logger.info(f"Starting Browser-Use task: {task}")
            
            # Initialize the browser agent
            agent = Agent(
                task=task,
                llm=self._get_llm_config(),
                browser_config={
                    "headless": self.headless,
                    "timeout": self.timeout * 1000,  # Convert to milliseconds
                    "viewport": {"width": 1920, "height": 1080}
                }
            )
            
            # Add context if provided
            if context:
                task_with_context = f"{task}\n\nContext: {json.dumps(context, indent=2)}"
            else:
                task_with_context = task
            
            # Execute the task
            if starting_url:
                result = await agent.run(task_with_context, starting_url=starting_url)
            else:
                result = await agent.run(task_with_context)
            
            # Process the result
            extracted_data = self._process_browser_result(result)
            
            return OSINTToolResult(
                success=True,
                data=extracted_data,
                source_url=starting_url,
                metadata={
                    "task": task,
                    "steps_taken": len(result.history) if hasattr(result, 'history') else 0,
                    "final_url": result.current_url if hasattr(result, 'current_url') else None,
                    "execution_time": result.execution_time if hasattr(result, 'execution_time') else None
                },
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            error_msg = f"Browser-Use task failed: {str(e)}"
            self.logger.error(error_msg)
            return OSINTToolResult(success=False, error=error_msg)
    
    def _get_llm_config(self) -> Dict[str, Any]:
        """Get LLM configuration for Browser-Use"""
        settings = get_settings()
        
        if settings.openai_api_key:
            return {
                "provider": "openai",
                "model": settings.default_llm_model,
                "api_key": settings.openai_api_key,
                "temperature": settings.temperature
            }
        elif settings.anthropic_api_key:
            return {
                "provider": "anthropic", 
                "model": "claude-3-sonnet-20240229",
                "api_key": settings.anthropic_api_key,
                "temperature": settings.temperature
            }
        else:
            raise ValueError("No LLM API key configured. Set OPENAI_API_KEY or ANTHROPIC_API_KEY.")
    
    def _process_browser_result(self, result) -> Dict[str, Any]:
        """Process Browser-Use result into structured data"""
        processed_data = {
            "task_completed": getattr(result, 'success', True),
            "final_message": getattr(result, 'message', ''),
            "extracted_text": getattr(result, 'extracted_text', ''),
            "screenshots": getattr(result, 'screenshots', []),
            "visited_urls": getattr(result, 'visited_urls', []),
            "form_data": getattr(result, 'form_data', {}),
            "clicked_elements": getattr(result, 'clicked_elements', [])
        }
        
        # Add execution history if available
        if hasattr(result, 'history'):
            processed_data["execution_steps"] = [
                {
                    "step": i + 1,
                    "action": step.get("action", ""),
                    "description": step.get("description", ""),
                    "success": step.get("success", True)
                }
                for i, step in enumerate(result.history)
            ]
        
        # Extract any structured data found
        if hasattr(result, 'structured_data'):
            processed_data["structured_data"] = result.structured_data
        
        return processed_data
    
    # Convenience methods for common OSINT tasks
    async def search_and_extract(self, search_query: str, search_engine: str = "google", 
                                num_results: int = 5) -> OSINTToolResult:
        """Search for information and extract results"""
        task = f"""
        1. Go to {search_engine}.com
        2. Search for: "{search_query}"
        3. Extract the first {num_results} search results including:
           - Title
           - URL
           - Snippet/description
           - Date (if available)
        4. Return the extracted data in a structured format
        """
        
        return await self._execute_async(task)
    
    async def extract_social_media_profile(self, profile_url: str, platform: str) -> OSINTToolResult:
        """Extract information from social media profiles"""
        task = f"""
        Navigate to {profile_url} and extract the following information from this {platform} profile:
        - Profile name/username
        - Bio/description
        - Follower/following counts
        - Recent posts (titles/content of last 5 posts)
        - Profile picture URL
        - Location information (if available)
        - Contact information (if available)
        - Any other publicly visible information
        
        Be respectful of privacy and only extract publicly visible information.
        """
        
        return await self._execute_async(task, starting_url=profile_url)
    
    async def monitor_website_changes(self, url: str, elements_to_monitor: List[str]) -> OSINTToolResult:
        """Monitor specific elements on a website for changes"""
        task = f"""
        Navigate to {url} and extract the current content of these elements:
        {', '.join(elements_to_monitor)}
        
        Return the current state of each element for comparison with future monitoring.
        """
        
        return await self._execute_async(task, starting_url=url)
    
    async def extract_forum_posts(self, forum_url: str, topic_keywords: List[str]) -> OSINTToolResult:
        """Extract posts from forums related to specific topics"""
        task = f"""
        Navigate to {forum_url} and search for posts related to these keywords: {', '.join(topic_keywords)}
        
        Extract information about relevant posts including:
        - Post title
        - Author username
        - Post date
        - Post content (first 200 characters)
        - Number of replies/views
        - Post URL
        
        Focus on recent posts (last 30 days if possible).
        """
        
        return await self._execute_async(task, starting_url=forum_url)
    
    async def investigate_domain(self, domain: str) -> OSINTToolResult:
        """Investigate a domain using various online tools"""
        task = f"""
        Investigate the domain {domain} by:
        1. Going to whois lookup tools to get registration information
        2. Checking DNS records if possible
        3. Looking up the domain on security/reputation sites
        4. Extracting any available information about:
           - Registration date
           - Registrar
           - Name servers
           - Contact information (if public)
           - Security reputation
           - Associated IP addresses
        
        Compile all findings into a structured report.
        """
        
        return await self._execute_async(task)
