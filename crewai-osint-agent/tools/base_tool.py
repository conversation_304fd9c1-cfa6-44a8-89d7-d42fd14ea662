"""
Base tool class for OSINT tools
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Union
from loguru import logger
from pydantic import BaseModel, Field


class OSINTToolResult(BaseModel):
    """Standard result format for OSINT tools"""
    success: bool = Field(description="Whether the operation was successful")
    data: Optional[Dict[str, Any]] = Field(default=None, description="The extracted data")
    error: Optional[str] = Field(default=None, description="Error message if operation failed")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata")
    source_url: Optional[str] = Field(default=None, description="Source URL if applicable")
    timestamp: Optional[str] = Field(default=None, description="Timestamp of the operation")


class BaseOSINTTool(ABC):
    """Base class for all OSINT tools"""
    
    def __init__(self, name: str, description: str, **kwargs):
        self.name = name
        self.description = description
        self.logger = logger.bind(tool=name)
    
    @abstractmethod
    async def _execute_async(self, *args, **kwargs) -> OSINTToolResult:
        """Execute the tool asynchronously"""
        pass
    
    def _run(self, *args, **kwargs) -> str:
        """Synchronous wrapper for async execution"""
        try:
            # Run the async method
            result = asyncio.run(self._execute_async(*args, **kwargs))
            
            if result.success:
                self.logger.info(f"Tool execution successful: {self.name}")
                return self._format_success_output(result)
            else:
                self.logger.error(f"Tool execution failed: {result.error}")
                return self._format_error_output(result)
                
        except Exception as e:
            self.logger.exception(f"Unexpected error in {self.name}")
            error_result = OSINTToolResult(
                success=False,
                error=f"Unexpected error: {str(e)}"
            )
            return self._format_error_output(error_result)
    
    def _format_success_output(self, result: OSINTToolResult) -> str:
        """Format successful result for CrewAI consumption"""
        output = f"✅ {self.name} executed successfully\n\n"
        
        if result.data:
            output += "📊 **Data:**\n"
            for key, value in result.data.items():
                if isinstance(value, (list, dict)):
                    output += f"- {key}: {len(value) if isinstance(value, list) else len(value.keys())} items\n"
                else:
                    output += f"- {key}: {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}\n"
            output += "\n"
        
        if result.source_url:
            output += f"🔗 **Source:** {result.source_url}\n\n"
        
        if result.metadata:
            output += "ℹ️ **Metadata:**\n"
            for key, value in result.metadata.items():
                output += f"- {key}: {value}\n"
        
        return output
    
    def _format_error_output(self, result: OSINTToolResult) -> str:
        """Format error result for CrewAI consumption"""
        return f"❌ {self.name} failed: {result.error}"
    
    def _safe_extract_text(self, element, max_length: int = 1000) -> str:
        """Safely extract text from various element types"""
        try:
            if hasattr(element, 'get_text'):
                text = element.get_text(strip=True)
            elif hasattr(element, 'text'):
                text = element.text.strip()
            else:
                text = str(element).strip()
            
            return text[:max_length] if len(text) > max_length else text
        except Exception:
            return ""
