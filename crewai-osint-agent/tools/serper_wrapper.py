"""
Serper.dev Google Search API wrapper for OSINT analysis
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
import httpx
from pydantic import Field

from .base_tool import BaseOSINTTool, OSINTToolResult
from ..config.settings import get_settings


class Serper<PERSON>rapper(BaseOSINTTool):
    """Wrapper for Serper.dev Google Search API"""
    
    api_key: str = Field(description="Serper API key")
    base_url: str = Field(default="https://google.serper.dev/search")
    timeout: int = Field(default=30)
    
    def __init__(self, api_key: Optional[str] = None, **kwargs):
        settings = get_settings()
        api_key = api_key or settings.serper_api_key
        
        if not api_key:
            raise ValueError("Serper API key is required. Set SERPER_API_KEY environment variable.")
        
        super().__init__(
            name="SerperSearch",
            description="Search Google using Serper.dev API for OSINT research. Useful for finding recent news, articles, and public information.",
            api_key=api_key,
            **kwargs
        )
    
    async def _execute_async(self, query: str, num_results: int = 10, 
                           search_type: str = "search", **kwargs) -> OSINTToolResult:
        """
        Execute Google search via Serper API
        
        Args:
            query: Search query string
            num_results: Number of results to return (max 100)
            search_type: Type of search (search, news, images, videos)
        """
        try:
            self.logger.info(f"Executing Serper search: {query}")
            
            # Prepare request
            headers = {
                "X-API-KEY": self.api_key,
                "Content-Type": "application/json"
            }
            
            payload = {
                "q": query,
                "num": min(num_results, 100),
                "gl": kwargs.get("country", "us"),
                "hl": kwargs.get("language", "en")
            }
            
            # Add date filter if specified
            if "date_filter" in kwargs:
                payload["tbs"] = f"qdr:{kwargs['date_filter']}"  # d=day, w=week, m=month, y=year
            
            # Select appropriate endpoint
            endpoint_map = {
                "search": "https://google.serper.dev/search",
                "news": "https://google.serper.dev/news", 
                "images": "https://google.serper.dev/images",
                "videos": "https://google.serper.dev/videos"
            }
            
            url = endpoint_map.get(search_type, self.base_url)
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(url, json=payload, headers=headers)
                response.raise_for_status()
                
                data = response.json()
                
                # Process results
                processed_results = self._process_search_results(data, search_type)
                
                return OSINTToolResult(
                    success=True,
                    data={
                        "query": query,
                        "search_type": search_type,
                        "results": processed_results,
                        "total_results": len(processed_results),
                        "raw_response": data
                    },
                    metadata={
                        "search_engine": "Google",
                        "api_provider": "Serper.dev",
                        "num_requested": num_results,
                        "num_returned": len(processed_results)
                    },
                    timestamp=datetime.now().isoformat()
                )
                
        except httpx.HTTPStatusError as e:
            error_msg = f"HTTP error {e.response.status_code}: {e.response.text}"
            self.logger.error(error_msg)
            return OSINTToolResult(success=False, error=error_msg)
            
        except Exception as e:
            error_msg = f"Search failed: {str(e)}"
            self.logger.error(error_msg)
            return OSINTToolResult(success=False, error=error_msg)
    
    def _process_search_results(self, data: Dict[str, Any], search_type: str) -> List[Dict[str, Any]]:
        """Process raw Serper response into structured results"""
        results = []
        
        if search_type == "search":
            # Regular search results
            organic_results = data.get("organic", [])
            for result in organic_results:
                processed = {
                    "title": result.get("title", ""),
                    "link": result.get("link", ""),
                    "snippet": result.get("snippet", ""),
                    "position": result.get("position", 0),
                    "date": result.get("date"),
                    "source": self._extract_domain(result.get("link", ""))
                }
                results.append(processed)
            
            # Add knowledge graph if available
            if "knowledgeGraph" in data:
                kg = data["knowledgeGraph"]
                results.insert(0, {
                    "title": f"Knowledge Graph: {kg.get('title', '')}",
                    "link": kg.get("website", ""),
                    "snippet": kg.get("description", ""),
                    "position": 0,
                    "type": "knowledge_graph",
                    "source": "Google Knowledge Graph"
                })
        
        elif search_type == "news":
            # News results
            news_results = data.get("news", [])
            for result in news_results:
                processed = {
                    "title": result.get("title", ""),
                    "link": result.get("link", ""),
                    "snippet": result.get("snippet", ""),
                    "date": result.get("date"),
                    "source": result.get("source", ""),
                    "imageUrl": result.get("imageUrl"),
                    "position": result.get("position", 0)
                }
                results.append(processed)
        
        return results
    
    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL"""
        try:
            from urllib.parse import urlparse
            return urlparse(url).netloc
        except:
            return url
    
    # Convenience methods for different search types
    async def search_news(self, query: str, num_results: int = 10, **kwargs) -> OSINTToolResult:
        """Search for news articles"""
        return await self._execute_async(query, num_results, "news", **kwargs)
    
    async def search_recent(self, query: str, timeframe: str = "d", num_results: int = 10, **kwargs) -> OSINTToolResult:
        """Search for recent results with time filter"""
        kwargs["date_filter"] = timeframe  # d=day, w=week, m=month
        return await self._execute_async(query, num_results, "search", **kwargs)
