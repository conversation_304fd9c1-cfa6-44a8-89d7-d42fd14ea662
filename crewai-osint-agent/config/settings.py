"""
Configuration settings for CrewAI OSINT Agents
"""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class OSINTSettings(BaseSettings):
    """Configuration settings for OSINT agents"""
    
    # API Keys
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    serper_api_key: Optional[str] = Field(default=None, env="SERPER_API_KEY")
    
    # Model Configuration
    default_llm_model: str = Field(default="gpt-4-turbo-preview", env="DEFAULT_LLM_MODEL")
    temperature: float = Field(default=0.1, env="LLM_TEMPERATURE")
    max_tokens: int = Field(default=4000, env="MAX_TOKENS")
    
    # Browser Configuration
    browser_headless: bool = Field(default=True, env="BROWSER_HEADLESS")
    browser_timeout: int = Field(default=30, env="BROWSER_TIMEOUT")
    
    # Crawling Configuration
    crawl_delay: float = Field(default=1.0, env="CRAWL_DELAY")
    max_pages_per_domain: int = Field(default=10, env="MAX_PAGES_PER_DOMAIN")
    user_agent: str = Field(
        default="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
        env="USER_AGENT"
    )
    
    # Output Configuration
    output_format: str = Field(default="json", env="OUTPUT_FORMAT")  # json, markdown, yaml
    save_raw_data: bool = Field(default=True, env="SAVE_RAW_DATA")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="logs/osint_agent.log", env="LOG_FILE")
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = OSINTSettings()


def get_settings() -> OSINTSettings:
    """Get the global settings instance"""
    return settings


def validate_api_keys() -> dict:
    """Validate that required API keys are present"""
    validation_results = {
        "openai": bool(settings.openai_api_key),
        "anthropic": bool(settings.anthropic_api_key),
        "serper": bool(settings.serper_api_key),
    }
    
    missing_keys = [key for key, present in validation_results.items() if not present]
    
    if missing_keys:
        print(f"⚠️  Missing API keys: {', '.join(missing_keys)}")
        print("Some features may not work without proper API keys.")
    
    return validation_results
