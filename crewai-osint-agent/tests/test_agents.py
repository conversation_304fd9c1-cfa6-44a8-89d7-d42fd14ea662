"""
Test suite for OSINT agents
"""

import pytest
from unittest.mock import Mock, patch

from agents import GeoOSINTAnalyst, CyberThreatOSINTAnalyst, BaseOSINTAgent


class TestBaseOSINTAgent:
    """Test cases for BaseOSINTAgent"""
    
    @patch('agents.base_agent.get_settings')
    def test_initialization(self, mock_settings):
        """Test agent initialization"""
        mock_settings.return_value.serper_api_key = "test_key"
        mock_settings.return_value.openai_api_key = "test_key"
        
        agent = BaseOSINTAgent(
            role="Test Agent",
            goal="Test goal",
            backstory="Test backstory"
        )
        
        assert agent.agent.role == "Test Agent"
        assert agent.agent.goal == "Test goal"
        assert agent.agent.backstory == "Test backstory"
        assert len(agent.list_tools()) > 0
    
    @patch('agents.base_agent.get_settings')
    def test_tool_management(self, mock_settings):
        """Test tool addition and removal"""
        mock_settings.return_value.serper_api_key = None
        mock_settings.return_value.openai_api_key = None
        
        agent = BaseOSINTAgent(
            role="Test Agent",
            goal="Test goal", 
            backstory="Test backstory"
        )
        
        initial_tool_count = len(agent.list_tools())
        
        # Test adding a mock tool
        mock_tool = Mock()
        mock_tool.name = "MockTool"
        agent.add_tool(mock_tool)
        
        assert len(agent.list_tools()) == initial_tool_count + 1
        assert "MockTool" in agent.list_tools()
        
        # Test removing the tool
        agent.remove_tool("MockTool")
        assert len(agent.list_tools()) == initial_tool_count
        assert "MockTool" not in agent.list_tools()
    
    @patch('agents.base_agent.get_settings')
    def test_capabilities(self, mock_settings):
        """Test capabilities reporting"""
        mock_settings.return_value.serper_api_key = "test_key"
        
        agent = BaseOSINTAgent(
            role="Test Agent",
            goal="Test goal",
            backstory="Test backstory"
        )
        
        capabilities = agent.get_capabilities()
        
        assert "role" in capabilities
        assert "goal" in capabilities
        assert "tools" in capabilities
        assert capabilities["role"] == "Test Agent"
        assert isinstance(capabilities["tools"], list)


class TestGeoOSINTAnalyst:
    """Test cases for GeoOSINTAnalyst"""
    
    @patch('agents.base_agent.get_settings')
    def test_initialization(self, mock_settings):
        """Test GeoOSINT analyst initialization"""
        mock_settings.return_value.serper_api_key = "test_key"
        
        analyst = GeoOSINTAnalyst()
        
        assert "Geopolitical OSINT Analyst" in analyst.agent.role
        assert "geopolitical" in analyst.agent.goal.lower()
        assert "political" in analyst.agent.backstory.lower()
    
    @patch('agents.base_agent.get_settings')
    def test_regional_conflict_analysis(self, mock_settings):
        """Test regional conflict analysis task creation"""
        mock_settings.return_value.serper_api_key = "test_key"
        
        analyst = GeoOSINTAnalyst()
        task_config = analyst.analyze_regional_conflict("Middle East", "30d")
        
        assert task_config["region"] == "Middle East"
        assert task_config["timeframe"] == "30d"
        assert task_config["analysis_type"] == "regional_conflict"
        assert "Middle East" in task_config["task"]
    
    @patch('agents.base_agent.get_settings')
    def test_political_actor_tracking(self, mock_settings):
        """Test political actor tracking task creation"""
        mock_settings.return_value.serper_api_key = "test_key"
        
        analyst = GeoOSINTAnalyst()
        task_config = analyst.track_political_actor("Test Leader", ["foreign_policy"])
        
        assert task_config["actor"] == "Test Leader"
        assert "foreign_policy" in task_config["focus_areas"]
        assert task_config["analysis_type"] == "political_actor_tracking"
    
    @patch('agents.base_agent.get_settings')
    def test_diplomatic_relations_assessment(self, mock_settings):
        """Test diplomatic relations assessment"""
        mock_settings.return_value.serper_api_key = "test_key"
        
        analyst = GeoOSINTAnalyst()
        task_config = analyst.assess_diplomatic_relations("Country A", "Country B")
        
        assert "Country A" in task_config["countries"]
        assert "Country B" in task_config["countries"]
        assert task_config["analysis_type"] == "diplomatic_relations"


class TestCyberThreatOSINTAnalyst:
    """Test cases for CyberThreatOSINTAnalyst"""
    
    @patch('agents.base_agent.get_settings')
    def test_initialization(self, mock_settings):
        """Test CTI OSINT analyst initialization"""
        mock_settings.return_value.serper_api_key = "test_key"
        
        analyst = CyberThreatOSINTAnalyst()
        
        assert "Cyber Threat Intelligence" in analyst.agent.role
        assert "cyber" in analyst.agent.goal.lower()
        assert "threat" in analyst.agent.backstory.lower()
    
    @patch('agents.base_agent.get_settings')
    def test_threat_actor_analysis(self, mock_settings):
        """Test threat actor analysis task creation"""
        mock_settings.return_value.serper_api_key = "test_key"
        
        analyst = CyberThreatOSINTAnalyst()
        task_config = analyst.analyze_threat_actor("APT28", ["ttps", "infrastructure"])
        
        assert task_config["actor"] == "APT28"
        assert "ttps" in task_config["focus_areas"]
        assert "infrastructure" in task_config["focus_areas"]
        assert task_config["analysis_type"] == "threat_actor_analysis"
    
    @patch('agents.base_agent.get_settings')
    def test_malware_campaign_tracking(self, mock_settings):
        """Test malware campaign tracking task creation"""
        mock_settings.return_value.serper_api_key = "test_key"
        
        analyst = CyberThreatOSINTAnalyst()
        task_config = analyst.track_malware_campaign("TestMalware", "60d")
        
        assert task_config["malware_family"] == "TestMalware"
        assert task_config["timeframe"] == "60d"
        assert task_config["analysis_type"] == "malware_campaign_tracking"
    
    @patch('agents.base_agent.get_settings')
    def test_security_incident_investigation(self, mock_settings):
        """Test security incident investigation task creation"""
        mock_settings.return_value.serper_api_key = "test_key"
        
        analyst = CyberThreatOSINTAnalyst()
        task_config = analyst.investigate_security_incident(
            "Data breach incident", 
            ["192.168.1.1", "malicious.domain.com"]
        )
        
        assert "Data breach incident" in task_config["incident"]
        assert "192.168.1.1" in task_config["known_iocs"]
        assert "malicious.domain.com" in task_config["known_iocs"]
        assert task_config["analysis_type"] == "incident_investigation"
    
    @patch('agents.base_agent.get_settings')
    def test_vulnerability_monitoring(self, mock_settings):
        """Test vulnerability monitoring task creation"""
        mock_settings.return_value.serper_api_key = "test_key"
        
        analyst = CyberThreatOSINTAnalyst()
        task_config = analyst.monitor_vulnerability_exploitation("CVE-2024-1234")
        
        assert task_config["cve_id"] == "CVE-2024-1234"
        assert task_config["analysis_type"] == "vulnerability_monitoring"
        assert "CVE-2024-1234" in task_config["task"]
    
    @patch('agents.base_agent.get_settings')
    def test_dark_web_monitoring(self, mock_settings):
        """Test dark web monitoring task creation"""
        mock_settings.return_value.serper_api_key = "test_key"
        
        analyst = CyberThreatOSINTAnalyst()
        task_config = analyst.analyze_dark_web_activity(
            ["ransomware", "exploit"], 
            ["forum1", "forum2"]
        )
        
        assert "ransomware" in task_config["keywords"]
        assert "exploit" in task_config["keywords"]
        assert "forum1" in task_config["forums"]
        assert task_config["analysis_type"] == "dark_web_monitoring"


if __name__ == "__main__":
    pytest.main([__file__])
