"""
Test suite for OSINT tools
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock

from tools import <PERSON><PERSON><PERSON><PERSON><PERSON>, Crawl4AI<PERSON><PERSON><PERSON>, BrowserUseWrapper
from tools.base_tool import OSINTToolResult


class TestSerperWrapper:
    """Test cases for Serper API wrapper"""
    
    @pytest.fixture
    def serper_tool(self):
        """Create SerperWrapper instance for testing"""
        with patch('tools.serper_wrapper.get_settings') as mock_settings:
            mock_settings.return_value.serper_api_key = "test_api_key"
            return SerperWrapper()
    
    @pytest.mark.asyncio
    async def test_search_success(self, serper_tool):
        """Test successful search execution"""
        mock_response = {
            "organic": [
                {
                    "title": "Test Result",
                    "link": "https://example.com",
                    "snippet": "Test snippet",
                    "position": 1
                }
            ]
        }
        
        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.post = AsyncMock()
            mock_client.return_value.__aenter__.return_value.post.return_value.json.return_value = mock_response
            mock_client.return_value.__aenter__.return_value.post.return_value.raise_for_status = Mock()
            
            result = await serper_tool._execute_async("test query")
            
            assert result.success is True
            assert "results" in result.data
            assert len(result.data["results"]) == 1
            assert result.data["results"][0]["title"] == "Test Result"
    
    @pytest.mark.asyncio
    async def test_search_failure(self, serper_tool):
        """Test search failure handling"""
        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.post = AsyncMock()
            mock_client.return_value.__aenter__.return_value.post.side_effect = Exception("API Error")
            
            result = await serper_tool._execute_async("test query")
            
            assert result.success is False
            assert "API Error" in result.error


class TestCrawl4AIWrapper:
    """Test cases for Crawl4AI wrapper"""
    
    @pytest.fixture
    def crawl4ai_tool(self):
        """Create Crawl4AIWrapper instance for testing"""
        return Crawl4AIWrapper()
    
    @pytest.mark.asyncio
    async def test_content_extraction_success(self, crawl4ai_tool):
        """Test successful content extraction"""
        mock_result = Mock()
        mock_result.markdown = "# Test Content\nThis is test content"
        mock_result.metadata = {"title": "Test Page"}
        mock_result.cleaned_html = "<h1>Test Content</h1>"
        mock_result.links = {"internal": [], "external": []}
        mock_result.media = {"images": []}
        mock_result.status_code = 200
        mock_result.response_headers = {}
        
        with patch('crawl4ai.AsyncWebCrawler') as mock_crawler:
            mock_crawler.return_value.__aenter__.return_value.arun = AsyncMock(return_value=mock_result)
            
            result = await crawl4ai_tool._execute_async("https://example.com", "content")
            
            assert result.success is True
            assert "title" in result.data
            assert result.data["title"] == "Test Page"
    
    @pytest.mark.asyncio
    async def test_links_extraction(self, crawl4ai_tool):
        """Test links extraction functionality"""
        mock_result = Mock()
        mock_result.links = {
            "internal": ["/page1", "/page2"],
            "external": ["https://external.com"]
        }
        mock_result.markdown = "Contact: <EMAIL> Phone: +1234567890"
        mock_result.status_code = 200
        mock_result.response_headers = {}
        mock_result.metadata = {}
        
        with patch('crawl4ai.AsyncWebCrawler') as mock_crawler:
            mock_crawler.return_value.__aenter__.return_value.arun = AsyncMock(return_value=mock_result)
            
            result = await crawl4ai_tool._execute_async("https://example.com", "links")
            
            assert result.success is True
            assert "internal_links" in result.data
            assert "external_links" in result.data
            assert "email_addresses" in result.data
            assert len(result.data["internal_links"]) == 2
            assert "<EMAIL>" in result.data["email_addresses"]


class TestBrowserUseWrapper:
    """Test cases for Browser-Use wrapper"""
    
    @pytest.fixture
    def browser_tool(self):
        """Create BrowserUseWrapper instance for testing"""
        with patch('tools.browser_use_wrapper.get_settings') as mock_settings:
            mock_settings.return_value.openai_api_key = "test_api_key"
            mock_settings.return_value.default_llm_model = "gpt-4"
            mock_settings.return_value.temperature = 0.1
            return BrowserUseWrapper()
    
    @pytest.mark.asyncio
    async def test_task_execution_success(self, browser_tool):
        """Test successful browser task execution"""
        mock_result = Mock()
        mock_result.success = True
        mock_result.message = "Task completed successfully"
        mock_result.extracted_text = "Extracted content"
        mock_result.current_url = "https://example.com"
        mock_result.execution_time = 10.5
        mock_result.history = [
            {"action": "navigate", "description": "Navigated to page", "success": True}
        ]
        
        with patch('browser_use.Agent') as mock_agent:
            mock_agent.return_value.run = AsyncMock(return_value=mock_result)
            
            result = await browser_tool._execute_async("Navigate to example.com")
            
            assert result.success is True
            assert "task_completed" in result.data
            assert result.data["task_completed"] is True
    
    @pytest.mark.asyncio
    async def test_search_and_extract(self, browser_tool):
        """Test search and extract convenience method"""
        mock_result = Mock()
        mock_result.success = True
        mock_result.extracted_text = "Search results content"
        
        with patch('browser_use.Agent') as mock_agent:
            mock_agent.return_value.run = AsyncMock(return_value=mock_result)
            
            result = await browser_tool.search_and_extract("test query")
            
            assert result.success is True


class TestOSINTToolResult:
    """Test cases for OSINTToolResult model"""
    
    def test_successful_result(self):
        """Test creating successful result"""
        result = OSINTToolResult(
            success=True,
            data={"key": "value"},
            source_url="https://example.com"
        )
        
        assert result.success is True
        assert result.data["key"] == "value"
        assert result.source_url == "https://example.com"
        assert result.error is None
    
    def test_failed_result(self):
        """Test creating failed result"""
        result = OSINTToolResult(
            success=False,
            error="Test error message"
        )
        
        assert result.success is False
        assert result.error == "Test error message"
        assert result.data is None


if __name__ == "__main__":
    pytest.main([__file__])
