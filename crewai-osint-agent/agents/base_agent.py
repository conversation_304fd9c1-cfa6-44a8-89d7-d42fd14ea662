"""
Base OSINT Agent class with common functionality
"""

from typing import List, Dict, Any, Optional
from crewai import Agent
from crewai_tools import SerperDevTool, ScrapeWebsiteTool
from loguru import logger

from config.settings import get_settings


class BaseOSINTAgent:
    """Base class for OSINT agents with common tools and functionality"""
    
    def __init__(self, role: str, goal: str, backstory: str,
                 tools: Optional[List[Any]] = None,
                 additional_tools: Optional[List[Any]] = None,
                 **kwargs):
        """
        Initialize base OSINT agent
        
        Args:
            role: Agent's role description
            goal: Agent's primary goal
            backstory: Agent's background story
            tools: Custom tools list (if None, uses default OSINT tools)
            additional_tools: Additional tools to add to default set
        """
        self.settings = get_settings()
        self.logger = logger.bind(agent=role)
        
        # Initialize default OSINT tools
        if tools is None:
            tools = self._get_default_tools()
        
        # Add additional tools if provided
        if additional_tools:
            tools.extend(additional_tools)
        
        # Create the CrewAI agent
        self.agent = Agent(
            role=role,
            goal=goal,
            backstory=backstory,
            tools=tools,
            verbose=True,
            allow_delegation=False,
            memory=True,
            **kwargs
        )
        
        self.logger.info(f"Initialized {role} agent with {len(tools)} tools")
    
    def _get_default_tools(self) -> List[Any]:
        """Get default OSINT tools"""
        tools = []

        try:
            # Add Serper search tool if API key is available
            if self.settings.serper_api_key:
                tools.append(SerperDevTool())
                self.logger.info("Added Serper search tool")
        except Exception as e:
            self.logger.warning(f"Failed to initialize Serper tool: {e}")

        try:
            # Add web scraping tool
            tools.append(ScrapeWebsiteTool())
            self.logger.info("Added web scraping tool")
        except Exception as e:
            self.logger.warning(f"Failed to initialize web scraping tool: {e}")

        return tools
    
    def get_agent(self) -> Agent:
        """Get the CrewAI agent instance"""
        return self.agent
    
    def add_tool(self, tool: Any):
        """Add a tool to the agent"""
        self.agent.tools.append(tool)
        self.logger.info(f"Added tool: {getattr(tool, 'name', str(tool))}")
    
    def remove_tool(self, tool_name: str):
        """Remove a tool by name"""
        self.agent.tools = [t for t in self.agent.tools if t.name != tool_name]
        self.logger.info(f"Removed tool: {tool_name}")
    
    def list_tools(self) -> List[str]:
        """List all available tools"""
        return [tool.name for tool in self.agent.tools]
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Get agent capabilities summary"""
        return {
            "role": self.agent.role,
            "goal": self.agent.goal,
            "tools": self.list_tools(),
            "memory_enabled": self.agent.memory,
            "delegation_allowed": self.agent.allow_delegation
        }
