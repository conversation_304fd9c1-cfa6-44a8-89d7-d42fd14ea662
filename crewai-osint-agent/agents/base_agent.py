"""
Base OSINT Agent class with common functionality
"""

from typing import List, Dict, Any, Optional
from crewai import Agent
from crewai_tools import BaseTool
from loguru import logger

from ..tools import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Crawl4<PERSON><PERSON><PERSON>rapper, <PERSON><PERSON>Wrapper, StagehandWrapper
from ..config.settings import get_settings


class BaseOSINTAgent:
    """Base class for OSINT agents with common tools and functionality"""
    
    def __init__(self, role: str, goal: str, backstory: str, 
                 tools: Optional[List[BaseTool]] = None,
                 additional_tools: Optional[List[BaseTool]] = None,
                 **kwargs):
        """
        Initialize base OSINT agent
        
        Args:
            role: Agent's role description
            goal: Agent's primary goal
            backstory: Agent's background story
            tools: Custom tools list (if None, uses default OSINT tools)
            additional_tools: Additional tools to add to default set
        """
        self.settings = get_settings()
        self.logger = logger.bind(agent=role)
        
        # Initialize default OSINT tools
        if tools is None:
            tools = self._get_default_tools()
        
        # Add additional tools if provided
        if additional_tools:
            tools.extend(additional_tools)
        
        # Create the CrewAI agent
        self.agent = Agent(
            role=role,
            goal=goal,
            backstory=backstory,
            tools=tools,
            verbose=True,
            allow_delegation=False,
            memory=True,
            **kwargs
        )
        
        self.logger.info(f"Initialized {role} agent with {len(tools)} tools")
    
    def _get_default_tools(self) -> List[BaseTool]:
        """Get default OSINT tools"""
        tools = []
        
        try:
            # Add Serper search tool if API key is available
            if self.settings.serper_api_key:
                tools.append(SerperWrapper())
                self.logger.info("Added Serper search tool")
        except Exception as e:
            self.logger.warning(f"Failed to initialize Serper tool: {e}")
        
        try:
            # Add Crawl4AI tool
            tools.append(Crawl4AIWrapper())
            self.logger.info("Added Crawl4AI tool")
        except Exception as e:
            self.logger.warning(f"Failed to initialize Crawl4AI tool: {e}")
        
        try:
            # Add Browser-Use tool if LLM is configured
            if self.settings.openai_api_key or self.settings.anthropic_api_key:
                tools.append(BrowserUseWrapper())
                self.logger.info("Added Browser-Use tool")
        except Exception as e:
            self.logger.warning(f"Failed to initialize Browser-Use tool: {e}")
        
        # Note: Stagehand is commented out as it requires special installation
        # try:
        #     tools.append(StagehandWrapper())
        #     self.logger.info("Added Stagehand tool")
        # except Exception as e:
        #     self.logger.warning(f"Failed to initialize Stagehand tool: {e}")
        
        return tools
    
    def get_agent(self) -> Agent:
        """Get the CrewAI agent instance"""
        return self.agent
    
    def add_tool(self, tool: BaseTool):
        """Add a tool to the agent"""
        self.agent.tools.append(tool)
        self.logger.info(f"Added tool: {tool.name}")
    
    def remove_tool(self, tool_name: str):
        """Remove a tool by name"""
        self.agent.tools = [t for t in self.agent.tools if t.name != tool_name]
        self.logger.info(f"Removed tool: {tool_name}")
    
    def list_tools(self) -> List[str]:
        """List all available tools"""
        return [tool.name for tool in self.agent.tools]
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Get agent capabilities summary"""
        return {
            "role": self.agent.role,
            "goal": self.agent.goal,
            "tools": self.list_tools(),
            "memory_enabled": self.agent.memory,
            "delegation_allowed": self.agent.allow_delegation
        }
