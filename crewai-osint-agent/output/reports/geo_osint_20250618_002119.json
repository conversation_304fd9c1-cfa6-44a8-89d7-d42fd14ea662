{"workflow_name": "geo_osint", "error": "1 validation error for Crew\n  Value error, Please provide an OpenAI API key. You can get one at https://platform.openai.com/account/api-keys [type=value_error, input_value={'agents': [Agent(role=Ge...': True, 'memory': True}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error", "success": false, "timestamp": "2025-06-18T00:21:19.078681"}