#!/usr/bin/env python3
"""
CrewAI OSINT Framework - Main Entry Point
Provides a command-line interface for running OSINT analyses
"""

import argparse
import sys
import json
from pathlib import Path
from datetime import datetime

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from workflows import GeoOSIN<PERSON>Workflow, CTIOSINTWorkflow
from config.settings import validate_api_keys, get_settings
from loguru import logger


def setup_logging(verbose: bool = False):
    """Setup logging configuration"""
    logger.remove()
    
    # Console logging
    log_level = "DEBUG" if verbose else "INFO"
    logger.add(
        sys.stdout,
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
        level=log_level
    )
    
    # File logging
    logger.add(
        "logs/main.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="10 MB"
    )


def check_setup():
    """Check if the framework is properly set up"""
    logger.info("Checking framework setup...")
    
    # Check API keys
    api_status = validate_api_keys()
    
    if not any(api_status.values()):
        logger.warning("⚠️  No API keys configured!")
        logger.info("Some features will be limited without API keys.")
        logger.info("Please configure API keys in .env file for full functionality.")
        return False
    
    # Report API status
    for service, available in api_status.items():
        status = "✅" if available else "❌"
        logger.info(f"{service.upper()}: {status}")
    
    return True


def run_geo_analysis(args):
    """Run geopolitical OSINT analysis"""
    logger.info(f"Starting geopolitical analysis: {args.target}")
    
    workflow = GeoOSINTWorkflow()
    
    if args.analysis_type == "regional":
        result = workflow.analyze_regional_situation(
            region=args.target,
            timeframe=args.timeframe,
            focus_areas=args.focus_areas.split(",") if args.focus_areas else None
        )
    elif args.analysis_type == "diplomatic":
        countries = args.target.split(",")
        if len(countries) != 2:
            logger.error("Diplomatic analysis requires two countries separated by comma")
            return False
        result = workflow.monitor_diplomatic_relations(countries[0].strip(), countries[1].strip())
    elif args.analysis_type == "crisis":
        result = workflow.track_political_crisis(args.target, args.description or "Political crisis")
    elif args.analysis_type == "election":
        result = workflow.assess_election_integrity(
            args.target, 
            args.date or datetime.now().strftime("%Y-%m-%d"),
            args.election_type or "general"
        )
    else:
        logger.error(f"Unknown analysis type: {args.analysis_type}")
        return False
    
    return result


def run_cti_analysis(args):
    """Run cyber threat intelligence analysis"""
    logger.info(f"Starting CTI analysis: {args.target}")
    
    workflow = CTIOSINTWorkflow()
    
    if args.analysis_type == "actor":
        result = workflow.analyze_threat_actor(
            actor_name=args.target,
            focus_areas=args.focus_areas.split(",") if args.focus_areas else None
        )
    elif args.analysis_type == "malware":
        result = workflow.investigate_malware_campaign(
            malware_family=args.target,
            timeframe=args.timeframe
        )
    elif args.analysis_type == "vulnerability":
        result = workflow.assess_vulnerability_threat(args.target)
    elif args.analysis_type == "ransomware":
        if args.target.lower() == "general":
            result = workflow.monitor_ransomware_activity(timeframe=args.timeframe)
        else:
            result = workflow.monitor_ransomware_activity(args.target, args.timeframe)
    else:
        logger.error(f"Unknown analysis type: {args.analysis_type}")
        return False
    
    return result


def save_result(result, output_file=None):
    """Save analysis result to file"""
    if not output_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"output/analysis_{timestamp}.json"
    
    # Ensure output directory exists
    Path(output_file).parent.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False, default=str)
        logger.info(f"📁 Results saved to: {output_file}")
        return output_file
    except Exception as e:
        logger.error(f"Failed to save results: {e}")
        return None


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="CrewAI OSINT Framework - Command Line Interface",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Geopolitical analysis
  python main.py geo regional "South Sudan" --timeframe 48h
  python main.py geo diplomatic "USA,China" 
  python main.py geo crisis "Myanmar" --description "Military coup"
  
  # Cyber threat intelligence
  python main.py cti actor "APT29" --focus-areas "recent_campaigns,ttps"
  python main.py cti malware "Emotet" --timeframe 30d
  python main.py cti vulnerability "CVE-2024-21412"
  python main.py cti ransomware "LockBit" --timeframe 30d
        """
    )
    
    parser.add_argument("-v", "--verbose", action="store_true", help="Enable verbose logging")
    parser.add_argument("-o", "--output", help="Output file path")
    parser.add_argument("--no-setup-check", action="store_true", help="Skip setup validation")
    
    subparsers = parser.add_subparsers(dest="domain", help="Analysis domain")
    
    # Geopolitical analysis
    geo_parser = subparsers.add_parser("geo", help="Geopolitical OSINT analysis")
    geo_parser.add_argument("analysis_type", choices=["regional", "diplomatic", "crisis", "election"])
    geo_parser.add_argument("target", help="Analysis target (region, countries, etc.)")
    geo_parser.add_argument("--timeframe", default="7d", help="Analysis timeframe (default: 7d)")
    geo_parser.add_argument("--focus-areas", help="Comma-separated focus areas")
    geo_parser.add_argument("--description", help="Description for crisis analysis")
    geo_parser.add_argument("--date", help="Date for election analysis")
    geo_parser.add_argument("--election-type", help="Type of election")
    
    # Cyber threat intelligence
    cti_parser = subparsers.add_parser("cti", help="Cyber threat intelligence analysis")
    cti_parser.add_argument("analysis_type", choices=["actor", "malware", "vulnerability", "ransomware"])
    cti_parser.add_argument("target", help="Analysis target (actor name, malware family, CVE, etc.)")
    cti_parser.add_argument("--timeframe", default="30d", help="Analysis timeframe (default: 30d)")
    cti_parser.add_argument("--focus-areas", help="Comma-separated focus areas")
    
    # Interactive mode
    interactive_parser = subparsers.add_parser("interactive", help="Interactive mode")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.verbose)
    
    logger.info("🧠 CrewAI OSINT Framework")
    logger.info("=" * 50)
    
    # Check setup unless skipped
    if not args.no_setup_check:
        setup_ok = check_setup()
        if not setup_ok:
            logger.warning("Setup issues detected. Use --no-setup-check to skip validation.")
    
    # Handle different modes
    if args.domain == "geo":
        result = run_geo_analysis(args)
    elif args.domain == "cti":
        result = run_cti_analysis(args)
    elif args.domain == "interactive":
        logger.info("Starting interactive mode...")
        from examples.interactive_demo import main as interactive_main
        interactive_main()
        return
    else:
        parser.print_help()
        return
    
    # Process results
    if result:
        if result.get('success'):
            logger.success("✅ Analysis completed successfully!")
            logger.info(f"Execution time: {result.get('execution_time', 0):.2f} seconds")
            logger.info(f"Tasks completed: {result.get('tasks_completed', 0)}")
            
            # Save results
            output_file = save_result(result, args.output)
            if output_file:
                logger.info(f"📊 View results: {output_file}")
        else:
            logger.error(f"❌ Analysis failed: {result.get('error', 'Unknown error')}")
            sys.exit(1)
    else:
        logger.error("❌ Analysis failed to execute")
        sys.exit(1)


if __name__ == "__main__":
    main()
