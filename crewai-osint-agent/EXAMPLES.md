# 📚 Usage Examples

This document provides comprehensive examples of using the CrewAI OSINT Framework for various analysis scenarios.

## 🌍 Geopolitical OSINT Examples

### Example 1: Regional Conflict Analysis

```python
from workflows import GeoOSINTWorkflow

# Initialize the workflow
geo_workflow = GeoOSINTWorkflow()

# Analyze recent developments in Ukraine
result = geo_workflow.analyze_regional_situation(
    region="Ukraine",
    timeframe="7d",
    focus_areas=[
        "military_developments",
        "international_support", 
        "diplomatic_efforts",
        "humanitarian_situation"
    ]
)

print(f"Analysis completed: {result['success']}")
print(f"Execution time: {result['execution_time']:.2f} seconds")
```

### Example 2: Political Crisis Monitoring

```python
# Track political developments in a specific country
result = geo_workflow.track_political_crisis(
    country="Myanmar",
    crisis_description="Military coup and ongoing civil unrest"
)

# The analysis will cover:
# - Background and timeline
# - Key political actors
# - International responses
# - Resolution prospects
```

### Example 3: Diplomatic Relations Assessment

```python
# Analyze bilateral relations
result = geo_workflow.monitor_diplomatic_relations(
    country1="India",
    country2="Pakistan", 
    context="Border tensions and Kashmir dispute"
)

# Monitor specific diplomatic events
result = geo_workflow.assess_diplomatic_relations(
    country1="United States",
    country2="Iran"
)
```

### Example 4: Election Integrity Analysis

```python
# Assess upcoming elections
result = geo_workflow.assess_election_integrity(
    country="Brazil",
    election_date="2024-10-06",
    election_type="presidential"
)

# The analysis includes:
# - Electoral framework assessment
# - Campaign environment evaluation
# - Security concerns
# - International monitoring reports
```

## 🔒 Cyber Threat Intelligence Examples

### Example 1: APT Group Analysis

```python
from workflows import CTIOSINTWorkflow

# Initialize CTI workflow
cti_workflow = CTIOSINTWorkflow()

# Comprehensive APT analysis
result = cti_workflow.analyze_threat_actor(
    actor_name="APT29",
    focus_areas=[
        "recent_campaigns",
        "ttps_analysis",
        "infrastructure", 
        "attribution_assessment"
    ]
)

# Analyze different APT groups
actors = ["APT28", "Lazarus Group", "FIN7", "Carbanak"]
for actor in actors:
    result = cti_workflow.analyze_threat_actor(actor)
    print(f"Analysis of {actor}: {'✅' if result['success'] else '❌'}")
```

### Example 2: Malware Campaign Investigation

```python
# Investigate specific malware families
malware_families = [
    "Emotet",
    "TrickBot", 
    "Ryuk",
    "Cobalt Strike",
    "QakBot"
]

for malware in malware_families:
    result = cti_workflow.investigate_malware_campaign(
        malware_family=malware,
        timeframe="60d"
    )
    
    if result['success']:
        print(f"✅ {malware} analysis completed")
        # Access structured data
        data = result.get('data', {})
        print(f"   Execution time: {result['execution_time']:.2f}s")
```

### Example 3: Vulnerability Threat Assessment

```python
# Assess critical vulnerabilities
critical_cves = [
    "CVE-2024-21412",  # Microsoft Exchange
    "CVE-2023-34362",  # MOVEit Transfer
    "CVE-2023-27997",  # Fortinet FortiGate
    "CVE-2023-3519"    # Citrix NetScaler
]

for cve in critical_cves:
    result = cti_workflow.assess_vulnerability_threat(cve)
    
    if result['success']:
        print(f"✅ {cve} threat assessment completed")
        # The analysis covers:
        # - Exploitation timeline
        # - Threat actor adoption
        # - In-the-wild activity
        # - Defensive recommendations
```

### Example 4: Ransomware Monitoring

```python
# Monitor general ransomware landscape
result = cti_workflow.monitor_ransomware_activity(timeframe="30d")

# Focus on specific ransomware groups
ransomware_groups = [
    "LockBit",
    "BlackCat", 
    "Royal",
    "Play",
    "Clop"
]

for group in ransomware_groups:
    result = cti_workflow.monitor_ransomware_activity(
        ransomware_group=group,
        timeframe="30d"
    )
    print(f"Monitoring {group}: {'✅' if result['success'] else '❌'}")
```

## 🔧 Advanced Usage Examples

### Example 1: Custom Analysis Pipeline

```python
from agents import GeoOSINTAnalyst, CyberThreatOSINTAnalyst
from workflows.base_workflow import BaseOSINTWorkflow
from crewai import Task

class CustomOSINTWorkflow(BaseOSINTWorkflow):
    def __init__(self):
        super().__init__("custom_analysis")
        self.geo_analyst = GeoOSINTAnalyst()
        self.cti_analyst = CyberThreatOSINTAnalyst()
    
    def hybrid_analysis(self, target_country: str, cyber_threat: str):
        """Combine geopolitical and cyber threat analysis"""
        
        # Task 1: Geopolitical context
        geo_task = self.create_task(
            description=f"""
            Analyze the current geopolitical situation in {target_country}
            focusing on factors that might influence cybersecurity threats.
            """,
            agent=self.geo_analyst.get_agent()
        )
        
        # Task 2: Cyber threat analysis
        cti_task = self.create_task(
            description=f"""
            Analyze cyber threats related to {cyber_threat} with specific
            focus on activities targeting {target_country}.
            """,
            agent=self.cti_analyst.get_agent(),
            context=[geo_task]
        )
        
        return self.execute_workflow(
            [geo_task, cti_task],
            [self.geo_analyst.get_agent(), self.cti_analyst.get_agent()]
        )

# Use the custom workflow
custom_workflow = CustomOSINTWorkflow()
result = custom_workflow.hybrid_analysis("Ukraine", "APT28")
```

### Example 2: Batch Analysis

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

def batch_threat_analysis():
    """Run multiple threat analyses in parallel"""
    
    cti_workflow = CTIOSINTWorkflow()
    
    # Define analysis targets
    targets = [
        ("APT29", ["recent_campaigns", "infrastructure"]),
        ("Lazarus Group", ["cryptocurrency_attacks", "supply_chain"]),
        ("FIN7", ["financial_targeting", "retail_attacks"]),
        ("Carbanak", ["banking_malware", "financial_fraud"])
    ]
    
    def analyze_actor(actor_data):
        actor_name, focus_areas = actor_data
        return cti_workflow.analyze_threat_actor(actor_name, focus_areas)
    
    # Run analyses in parallel
    with ThreadPoolExecutor(max_workers=2) as executor:
        results = list(executor.map(analyze_actor, targets))
    
    # Process results
    for i, (actor_name, _) in enumerate(targets):
        result = results[i]
        status = "✅" if result.get('success') else "❌"
        print(f"{status} {actor_name}: {result.get('execution_time', 0):.2f}s")
    
    return results

# Run batch analysis
batch_results = batch_threat_analysis()
```

### Example 3: Continuous Monitoring

```python
import time
import schedule
from datetime import datetime

class ContinuousMonitoring:
    def __init__(self):
        self.geo_workflow = GeoOSINTWorkflow()
        self.cti_workflow = CTIOSINTWorkflow()
    
    def daily_threat_briefing(self):
        """Generate daily threat intelligence briefing"""
        print(f"🔍 Starting daily briefing - {datetime.now()}")
        
        # Monitor ransomware activity
        ransomware_result = self.cti_workflow.monitor_ransomware_activity("24h")
        
        # Check for new vulnerabilities
        vuln_result = self.cti_workflow.assess_vulnerability_threat("CVE-2024-LATEST")
        
        # Geopolitical hotspots
        hotspots = ["Ukraine", "Middle East", "South China Sea"]
        for region in hotspots:
            geo_result = self.geo_workflow.analyze_regional_situation(region, "24h")
            print(f"   📍 {region}: {'✅' if geo_result.get('success') else '❌'}")
        
        print("📊 Daily briefing completed")
    
    def weekly_deep_dive(self):
        """Weekly comprehensive analysis"""
        print(f"🔬 Starting weekly deep dive - {datetime.now()}")
        
        # Comprehensive APT analysis
        apt_groups = ["APT29", "APT28", "Lazarus Group"]
        for apt in apt_groups:
            result = self.cti_workflow.analyze_threat_actor(apt)
            print(f"   🎯 {apt}: {'✅' if result.get('success') else '❌'}")
        
        print("📈 Weekly analysis completed")

# Setup monitoring schedule
monitor = ContinuousMonitoring()

# Schedule tasks
schedule.every().day.at("09:00").do(monitor.daily_threat_briefing)
schedule.every().monday.at("10:00").do(monitor.weekly_deep_dive)

# Run scheduler (in production, use proper task scheduler)
# while True:
#     schedule.run_pending()
#     time.sleep(60)
```

## 📊 Working with Results

### Example 1: Processing Analysis Results

```python
def process_analysis_result(result):
    """Extract and process analysis results"""
    
    if not result.get('success'):
        print(f"❌ Analysis failed: {result.get('error')}")
        return None
    
    # Extract metadata
    metadata = {
        'workflow': result.get('workflow_name'),
        'execution_time': result.get('execution_time'),
        'timestamp': result.get('start_time'),
        'tasks_completed': result.get('tasks_completed')
    }
    
    # Extract analysis content
    analysis_content = result.get('result', '')
    
    # Parse structured data if available
    if 'structured_data' in result:
        structured = result['structured_data']
        print(f"📊 Structured data available: {len(structured)} items")
    
    return {
        'metadata': metadata,
        'content': analysis_content,
        'raw_result': result
    }

# Use with any workflow result
geo_result = geo_workflow.analyze_regional_situation("Syria", "7d")
processed = process_analysis_result(geo_result)
```

### Example 2: Exporting Results

```python
import json
import csv
from datetime import datetime

def export_results(results, format='json'):
    """Export analysis results in various formats"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    if format == 'json':
        filename = f"osint_analysis_{timestamp}.json"
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
    
    elif format == 'csv':
        filename = f"osint_analysis_{timestamp}.csv"
        with open(filename, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['Workflow', 'Success', 'Execution Time', 'Timestamp'])
            
            for result in results:
                writer.writerow([
                    result.get('workflow_name', ''),
                    result.get('success', False),
                    result.get('execution_time', 0),
                    result.get('start_time', '')
                ])
    
    print(f"📁 Results exported to: {filename}")
    return filename

# Export multiple results
results = [geo_result, cti_result]
export_results(results, 'json')
export_results(results, 'csv')
```

## 🎯 Best Practices

### 1. Error Handling
```python
def robust_analysis(workflow, analysis_func, *args, **kwargs):
    """Wrapper for robust analysis execution"""
    max_retries = 3
    
    for attempt in range(max_retries):
        try:
            result = analysis_func(*args, **kwargs)
            
            if result.get('success'):
                return result
            else:
                print(f"Attempt {attempt + 1} failed: {result.get('error')}")
                
        except Exception as e:
            print(f"Attempt {attempt + 1} error: {e}")
            
        if attempt < max_retries - 1:
            time.sleep(2 ** attempt)  # Exponential backoff
    
    return {'success': False, 'error': 'Max retries exceeded'}
```

### 2. Resource Management
```python
def managed_analysis():
    """Analysis with proper resource management"""
    
    try:
        # Initialize workflows
        geo_workflow = GeoOSINTWorkflow()
        cti_workflow = CTIOSINTWorkflow()
        
        # Perform analysis
        results = []
        
        # Batch operations to avoid rate limits
        for region in ["Ukraine", "Syria", "Yemen"]:
            result = geo_workflow.analyze_regional_situation(region, "24h")
            results.append(result)
            time.sleep(1)  # Rate limiting
        
        return results
        
    except Exception as e:
        print(f"Analysis error: {e}")
        return []
    
    finally:
        # Cleanup if needed
        print("Analysis session completed")
```

### 3. Configuration Management
```python
from config.settings import get_settings

def configure_for_environment(env='development'):
    """Configure framework for different environments"""
    
    settings = get_settings()
    
    if env == 'development':
        # More verbose logging, shorter timeouts
        settings.log_level = 'DEBUG'
        settings.browser_timeout = 15
        settings.max_pages_per_domain = 3
        
    elif env == 'production':
        # Optimized for performance
        settings.log_level = 'INFO'
        settings.browser_timeout = 30
        settings.max_pages_per_domain = 10
        
    elif env == 'testing':
        # Minimal resource usage
        settings.browser_timeout = 10
        settings.max_pages_per_domain = 1
    
    return settings
```

---

These examples demonstrate the flexibility and power of the CrewAI OSINT Framework. Start with the basic examples and gradually explore more advanced usage patterns as you become familiar with the system.
