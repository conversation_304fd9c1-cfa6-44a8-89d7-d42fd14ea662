"""
Base workflow class for OSINT analysis
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

from crewai import Crew, Task
from loguru import logger

from ..config.settings import get_settings


class BaseOSINTWorkflow:
    """Base class for OSINT analysis workflows"""
    
    def __init__(self, workflow_name: str, output_dir: Optional[str] = None):
        """
        Initialize base workflow
        
        Args:
            workflow_name: Name of the workflow
            output_dir: Directory to save outputs (defaults to output/reports)
        """
        self.workflow_name = workflow_name
        self.settings = get_settings()
        self.logger = logger.bind(workflow=workflow_name)
        
        # Setup output directory
        if output_dir is None:
            output_dir = Path("output/reports")
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"Initialized {workflow_name} workflow")
    
    def create_task(self, description: str, agent, expected_output: str = None, 
                   context: Optional[List[Task]] = None) -> Task:
        """
        Create a CrewAI task
        
        Args:
            description: Task description
            agent: Agent to execute the task
            expected_output: Expected output format
            context: Previous tasks that provide context
        """
        if expected_output is None:
            expected_output = """
            A comprehensive OSINT analysis report in markdown format including:
            - Executive summary
            - Key findings
            - Detailed analysis
            - Sources and references
            - Recommendations
            - Appendices with raw data
            """
        
        task = Task(
            description=description,
            agent=agent,
            expected_output=expected_output,
            context=context or []
        )
        
        return task
    
    def execute_workflow(self, tasks: List[Task], agents: List, 
                        verbose: bool = True) -> Dict[str, Any]:
        """
        Execute a workflow with given tasks and agents
        
        Args:
            tasks: List of tasks to execute
            agents: List of agents
            verbose: Whether to enable verbose output
        """
        try:
            self.logger.info(f"Starting workflow execution with {len(tasks)} tasks")
            
            # Create crew
            crew = Crew(
                agents=agents,
                tasks=tasks,
                verbose=verbose,
                memory=True
            )
            
            # Execute workflow
            start_time = datetime.now()
            result = crew.kickoff()
            end_time = datetime.now()
            
            execution_time = (end_time - start_time).total_seconds()
            
            # Prepare workflow result
            workflow_result = {
                "workflow_name": self.workflow_name,
                "execution_time": execution_time,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "tasks_completed": len(tasks),
                "result": str(result),
                "success": True
            }
            
            # Save results
            self._save_workflow_result(workflow_result)
            
            self.logger.info(f"Workflow completed successfully in {execution_time:.2f} seconds")
            return workflow_result
            
        except Exception as e:
            error_result = {
                "workflow_name": self.workflow_name,
                "error": str(e),
                "success": False,
                "timestamp": datetime.now().isoformat()
            }
            
            self._save_workflow_result(error_result)
            self.logger.error(f"Workflow failed: {e}")
            return error_result
    
    def _save_workflow_result(self, result: Dict[str, Any]):
        """Save workflow result to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.workflow_name}_{timestamp}.json"
        filepath = self.output_dir / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"Workflow result saved to: {filepath}")
            
            # Also save as markdown if result contains analysis
            if result.get("success") and "result" in result:
                self._save_markdown_report(result, timestamp)
                
        except Exception as e:
            self.logger.error(f"Failed to save workflow result: {e}")
    
    def _save_markdown_report(self, result: Dict[str, Any], timestamp: str):
        """Save workflow result as markdown report"""
        try:
            filename = f"{self.workflow_name}_{timestamp}.md"
            filepath = self.output_dir / filename
            
            markdown_content = self._format_markdown_report(result)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            self.logger.info(f"Markdown report saved to: {filepath}")
            
        except Exception as e:
            self.logger.error(f"Failed to save markdown report: {e}")
    
    def _format_markdown_report(self, result: Dict[str, Any]) -> str:
        """Format workflow result as markdown"""
        content = f"""# {self.workflow_name} - OSINT Analysis Report

## Execution Summary

- **Workflow**: {result.get('workflow_name', 'Unknown')}
- **Start Time**: {result.get('start_time', 'Unknown')}
- **End Time**: {result.get('end_time', 'Unknown')}
- **Execution Time**: {result.get('execution_time', 0):.2f} seconds
- **Tasks Completed**: {result.get('tasks_completed', 0)}
- **Status**: {'✅ Success' if result.get('success') else '❌ Failed'}

## Analysis Results

{result.get('result', 'No results available')}

## Metadata

```json
{json.dumps({k: v for k, v in result.items() if k != 'result'}, indent=2, default=str)}
```

---
*Report generated by CrewAI OSINT Framework*
"""
        return content
    
    def list_previous_reports(self) -> List[Dict[str, Any]]:
        """List previous workflow reports"""
        reports = []
        
        for file_path in self.output_dir.glob(f"{self.workflow_name}_*.json"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    report_data = json.load(f)
                    report_data['file_path'] = str(file_path)
                    reports.append(report_data)
            except Exception as e:
                self.logger.warning(f"Failed to load report {file_path}: {e}")
        
        # Sort by timestamp
        reports.sort(key=lambda x: x.get('start_time', ''), reverse=True)
        return reports
    
    def get_latest_report(self) -> Optional[Dict[str, Any]]:
        """Get the latest workflow report"""
        reports = self.list_previous_reports()
        return reports[0] if reports else None
