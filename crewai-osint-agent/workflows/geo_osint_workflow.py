"""
Geopolitical OSINT Analysis Workflow
"""

from typing import Dict, Any, Optional, List
from crewai import Task

from .base_workflow import BaseOSINTWorkflow
from agents import GeoOSINTAnalyst


class GeoOSINTWorkflow(BaseOSINTWorkflow):
    """Workflow for geopolitical OSINT analysis"""
    
    def __init__(self, output_dir: Optional[str] = None):
        super().__init__("geo_osint", output_dir)
        self.analyst = GeoOSINTAnalyst()
    
    def analyze_regional_situation(self, region: str, timeframe: str = "7d", 
                                 focus_areas: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Analyze current situation in a specific region
        
        Args:
            region: Geographic region to analyze
            timeframe: Time period for analysis
            focus_areas: Specific areas to focus on
        """
        focus_areas = focus_areas or [
            "political_developments", 
            "security_situation", 
            "economic_impacts",
            "international_responses"
        ]
        
        # Create analysis task
        task_description = f"""
        Conduct a comprehensive geopolitical OSINT analysis of {region} over the last {timeframe}.
        
        Focus areas: {', '.join(focus_areas)}
        
        Your analysis should include:
        
        1. **Executive Summary**
           - Key developments and their significance
           - Overall assessment of the situation
           - Critical trends and patterns
        
        2. **Recent Events Analysis**
           - Chronological timeline of major events
           - Key political, military, and diplomatic developments
           - Significant statements by leaders and officials
        
        3. **Actor Analysis**
           - Key political figures and their positions
           - International actors and their involvement
           - Non-state actors and their influence
        
        4. **Security Assessment**
           - Current security situation and threats
           - Military activities and deployments
           - Conflict escalation/de-escalation indicators
        
        5. **International Response**
           - Diplomatic initiatives and statements
           - Economic measures and sanctions
           - International organization involvement
        
        6. **Economic Impact**
           - Economic consequences of recent events
           - Trade and investment implications
           - Humanitarian and social impacts
        
        7. **Future Outlook**
           - Potential scenarios and developments
           - Key indicators to monitor
           - Risk assessment and implications
        
        Use multiple reliable sources and cross-reference information. Provide source citations for all claims.
        Focus on factual, objective analysis based on open source intelligence.
        """
        
        expected_output = """
        A comprehensive geopolitical intelligence report in markdown format with:
        - Executive summary (2-3 paragraphs)
        - Detailed analysis sections as outlined
        - Source citations and references
        - Key findings and recommendations
        - Appendix with raw data and additional sources
        """
        
        task = self.create_task(
            description=task_description,
            agent=self.analyst.get_agent(),
            expected_output=expected_output
        )
        
        return self.execute_workflow([task], [self.analyst.get_agent()])
    
    def track_political_crisis(self, country: str, crisis_description: str) -> Dict[str, Any]:
        """
        Track and analyze a political crisis
        
        Args:
            country: Country experiencing the crisis
            crisis_description: Brief description of the crisis
        """
        task_description = f"""
        Analyze the ongoing political crisis in {country}: {crisis_description}
        
        Provide comprehensive analysis covering:
        
        1. **Crisis Overview**
           - Background and root causes
           - Timeline of key events
           - Current status and developments
        
        2. **Key Actors**
           - Government officials and their positions
           - Opposition leaders and movements
           - International actors and their stances
        
        3. **Political Dynamics**
           - Power struggles and alliances
           - Public opinion and protests
           - Media coverage and narratives
        
        4. **International Implications**
           - Regional stability concerns
           - International community responses
           - Economic and diplomatic consequences
        
        5. **Resolution Prospects**
           - Potential solutions and compromises
           - Mediation efforts and initiatives
           - Obstacles to resolution
        
        6. **Risk Assessment**
           - Escalation scenarios
           - Spillover effects
           - Long-term implications
        
        Use recent news, official statements, and credible analysis sources.
        """
        
        task = self.create_task(
            description=task_description,
            agent=self.analyst.get_agent()
        )
        
        return self.execute_workflow([task], [self.analyst.get_agent()])
    
    def monitor_diplomatic_relations(self, country1: str, country2: str, 
                                   context: Optional[str] = None) -> Dict[str, Any]:
        """
        Monitor diplomatic relations between two countries
        
        Args:
            country1: First country
            country2: Second country
            context: Additional context about the relationship
        """
        context_text = f"\n\nContext: {context}" if context else ""
        
        task_description = f"""
        Analyze current diplomatic relations between {country1} and {country2}.{context_text}
        
        Analysis framework:
        
        1. **Relationship Overview**
           - Historical context and background
           - Current state of relations
           - Recent changes and developments
        
        2. **Recent Diplomatic Activity**
           - High-level meetings and visits
           - Official statements and communications
           - Diplomatic initiatives and agreements
        
        3. **Areas of Cooperation**
           - Trade and economic partnerships
           - Security and defense cooperation
           - Cultural and educational exchanges
        
        4. **Points of Tension**
           - Disputes and disagreements
           - Competing interests and priorities
           - Public criticisms and concerns
        
        5. **Third-Party Perspectives**
           - Regional actors' views
           - International community assessment
           - Media and expert analysis
        
        6. **Future Trajectory**
           - Upcoming events and milestones
           - Potential for improvement or deterioration
           - Key factors influencing the relationship
        
        Provide balanced analysis based on official sources and credible reporting.
        """
        
        task = self.create_task(
            description=task_description,
            agent=self.analyst.get_agent()
        )
        
        return self.execute_workflow([task], [self.analyst.get_agent()])
    
    def assess_election_integrity(self, country: str, election_date: str, 
                                election_type: str = "general") -> Dict[str, Any]:
        """
        Assess election integrity and democratic processes
        
        Args:
            country: Country holding elections
            election_date: Date of the election
            election_type: Type of election
        """
        task_description = f"""
        Assess the integrity of the {election_type} election in {country} scheduled for {election_date}.
        
        Assessment areas:
        
        1. **Electoral Framework**
           - Electoral laws and regulations
           - Election administration and oversight
           - Voter registration and eligibility
        
        2. **Campaign Environment**
           - Media freedom and access
           - Campaign finance transparency
           - Equal opportunity for candidates
        
        3. **Security Concerns**
           - Threats to electoral process
           - Violence against candidates/voters
           - Security measures and preparations
        
        4. **International Monitoring**
           - Observer missions and reports
           - International assessments
           - Diplomatic concerns and statements
        
        5. **Irregularities and Concerns**
           - Reported violations or issues
           - Opposition complaints and allegations
           - Civil society monitoring reports
        
        6. **Democratic Standards**
           - Compliance with international norms
           - Transparency and accountability measures
           - Post-election dispute resolution
        
        Base analysis on reports from election observers, civil society, and credible media sources.
        """
        
        task = self.create_task(
            description=task_description,
            agent=self.analyst.get_agent()
        )
        
        return self.execute_workflow([task], [self.analyst.get_agent()])
