# 🧠 CrewAI OSINT Agent Framework

A comprehensive **Open Source Intelligence (OSINT)** analysis framework built with CrewAI, featuring specialized AI agents for **Geopolitical Analysis** and **Cyber Threat Intelligence**. This framework automates intelligence gathering, analysis, and reporting using advanced AI agents equipped with web search, content extraction, and browser automation capabilities.

## 🎯 Overview

The CrewAI OSINT Framework is a production-ready intelligence analysis system that combines the power of Large Language Models (LLMs) with specialized OSINT tools to provide automated, comprehensive intelligence analysis. The framework is designed for intelligence analysts, security researchers, journalists, and organizations requiring systematic open source intelligence capabilities.

### 🤖 **Specialized AI Agents**

#### **🌍 Geopolitical OSINT Analyst**
An expert AI agent specialized in analyzing political events, conflicts, diplomatic relations, and regional developments. This agent provides comprehensive geopolitical intelligence with focus on:
- Regional conflict analysis and monitoring
- Political crisis tracking and assessment
- Diplomatic relations evaluation
- Election integrity monitoring
- Sanctions impact analysis
- International response coordination

#### **🔒 Cyber Threat Intelligence OSINT Analyst**
A specialized AI agent focused on cybersecurity threats, malware analysis, and digital forensics. This agent delivers actionable cyber threat intelligence including:
- Advanced Persistent Threat (APT) group analysis
- Malware campaign investigation
- Vulnerability threat assessment
- Ransomware activity monitoring
- Dark web intelligence gathering
- Incident response support

Both agents leverage multiple data-gathering tools and provide structured, actionable intelligence reports with source citations and confidence assessments.

## ✨ Features

### 🔧 Tool Integration
- **Serper.dev**: Google Search API for comprehensive web search
- **Crawl4AI**: Intelligent web crawling and content extraction
- **Browser-Use**: Advanced browser automation for complex interactions
- **Stagehand**: Multi-agent browser environments (placeholder - requires manual setup)

### 🤖 Agent Capabilities
- **Structured Analysis**: Comprehensive reports with executive summaries, detailed findings, and recommendations
- **Multi-Source Intelligence**: Combines data from multiple sources for accurate analysis
- **Automated Workflows**: End-to-end analysis pipelines with minimal human intervention
- **Flexible Configuration**: Customizable analysis parameters and focus areas

### 📊 Output Formats
- **Markdown Reports**: Human-readable analysis reports
- **JSON Data**: Structured data for further processing
- **IOC Extraction**: Indicators of Compromise for cybersecurity use cases
- **Timeline Analysis**: Chronological event tracking

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Virtual environment (recommended)
- API keys for external services (optional but recommended)

### Installation

1. **Clone and setup the project:**
```bash
git clone <repository-url>
cd Agent01

# Create and activate virtual environment
python3 -m venv crewai-osint-env
source crewai-osint-env/bin/activate  # On Windows: crewai-osint-env\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install Playwright browsers
playwright install
```

2. **Configure API keys:**
```bash
# Copy the example environment file
cp .env.example .env

# Edit .env with your API keys
nano .env
```

3. **Run the interactive demo:**
```bash
cd crewai-osint-agent
python examples/interactive_demo.py
```

## 🔑 Configuration

### Environment Variables

Create a `.env` file with the following configuration:

```env
# Language Model API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Search API Keys  
SERPER_API_KEY=your_serper_api_key_here

# Model Configuration
DEFAULT_LLM_MODEL=gpt-4-turbo-preview
LLM_TEMPERATURE=0.1
MAX_TOKENS=4000

# Browser Configuration
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30

# Crawling Configuration
CRAWL_DELAY=1.0
MAX_PAGES_PER_DOMAIN=10

# Output Configuration
OUTPUT_FORMAT=json
SAVE_RAW_DATA=true
LOG_LEVEL=INFO
```

### API Key Setup

#### Required for Full Functionality:
- **OpenAI API Key**: For LLM-powered analysis and Browser-Use tool
- **Serper API Key**: For Google search capabilities

#### Optional:
- **Anthropic API Key**: Alternative to OpenAI for Claude models

### Getting API Keys:
1. **OpenAI**: Visit [OpenAI API](https://platform.openai.com/api-keys)
2. **Serper**: Visit [Serper.dev](https://serper.dev/) 
3. **Anthropic**: Visit [Anthropic Console](https://console.anthropic.com/)

## 📖 Usage Examples

### Geopolitical Analysis

```python
from workflows import GeoOSINTWorkflow

# Initialize workflow
geo_workflow = GeoOSINTWorkflow()

# Analyze regional situation
result = geo_workflow.analyze_regional_situation(
    region="South Sudan",
    timeframe="48h",
    focus_areas=["political_developments", "security_situation"]
)

# Monitor diplomatic relations
result = geo_workflow.monitor_diplomatic_relations(
    country1="United States",
    country2="China",
    context="Trade tensions and strategic competition"
)
```

### Cyber Threat Intelligence

```python
from workflows import CTIOSINTWorkflow

# Initialize workflow
cti_workflow = CTIOSINTWorkflow()

# Analyze threat actor
result = cti_workflow.analyze_threat_actor(
    actor_name="APT29",
    focus_areas=["recent_campaigns", "ttps_analysis", "infrastructure"]
)

# Investigate malware campaign
result = cti_workflow.investigate_malware_campaign(
    malware_family="Emotet",
    timeframe="60d"
)

# Assess vulnerability threat
result = cti_workflow.assess_vulnerability_threat(
    cve_id="CVE-2024-21412"
)
```

### Running Examples

```bash
# Run geopolitical analysis examples
python examples/geo_osint_example.py

# Run cyber threat intelligence examples  
python examples/cti_osint_example.py

# Interactive demo with menu-driven interface
python examples/interactive_demo.py
```

## 🏗️ Architecture

### Project Structure
```
crewai-osint-agent/
├── agents/                 # Agent definitions
│   ├── base_agent.py      # Base agent class
│   ├── geo_analyst.py     # Geopolitical analyst
│   └── cti_analyst.py     # CTI analyst
├── tools/                 # Tool wrappers
│   ├── base_tool.py       # Base tool class
│   ├── serper_wrapper.py  # Serper API wrapper
│   ├── crawl4ai_wrapper.py # Crawl4AI wrapper
│   ├── browser_use_wrapper.py # Browser-Use wrapper
│   └── stagehand_wrapper.py # Stagehand wrapper
├── workflows/             # Analysis workflows
│   ├── base_workflow.py   # Base workflow class
│   ├── geo_osint_workflow.py # Geo analysis workflow
│   └── cti_osint_workflow.py # CTI analysis workflow
├── config/                # Configuration
│   └── settings.py        # Settings management
├── examples/              # Usage examples
├── tests/                 # Test suite
└── output/               # Generated reports
    └── reports/
```

### Component Overview

#### Agents
- **BaseOSINTAgent**: Common functionality for all OSINT agents
- **GeoOSINTAnalyst**: Specialized for geopolitical analysis
- **CyberThreatOSINTAnalyst**: Specialized for cyber threat intelligence

#### Tools
- **SerperWrapper**: Google search via Serper.dev API
- **Crawl4AIWrapper**: Intelligent web crawling and extraction
- **BrowserUseWrapper**: Advanced browser automation
- **StagehandWrapper**: Multi-agent browser environments (placeholder)

#### Workflows
- **BaseOSINTWorkflow**: Common workflow functionality
- **GeoOSINTWorkflow**: Geopolitical analysis pipelines
- **CTIOSINTWorkflow**: Cyber threat intelligence pipelines

## 🧪 Testing

Run the test suite:

```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run all tests
pytest tests/

# Run specific test files
pytest tests/test_tools.py
pytest tests/test_agents.py

# Run with coverage
pytest --cov=crewai-osint-agent tests/
```

## 📊 Output Examples

### Geopolitical Analysis Report
```markdown
# Geopolitical OSINT Analysis - South Sudan

## Executive Summary
Recent political developments in South Sudan show increased tensions...

## Key Findings
1. Political actor movements and statements
2. Security situation assessment  
3. International community responses
4. Economic impact analysis

## Detailed Analysis
[Comprehensive analysis sections...]

## Recommendations
[Actionable recommendations...]
```

### CTI Analysis Report
```markdown
# Cyber Threat Intelligence Analysis - APT29

## Executive Summary
APT29 (Cozy Bear) has demonstrated continued activity targeting...

## Threat Actor Profile
- **Attribution**: Russian Foreign Intelligence Service (SVR)
- **Active Since**: 2008
- **Primary Targets**: Government, diplomatic, defense sectors

## Recent Campaign Analysis
[Detailed campaign information...]

## TTPs Analysis (MITRE ATT&CK)
- **Initial Access**: T1566.001 (Spearphishing Attachment)
- **Execution**: T1059.001 (PowerShell)
[Additional TTPs...]

## IOCs
[Structured indicators of compromise...]
```

## 🔧 Customization

### Adding Custom Tools

```python
from tools.base_tool import BaseOSINTTool, OSINTToolResult

class CustomTool(BaseOSINTTool):
    def __init__(self):
        super().__init__(
            name="CustomTool",
            description="Custom OSINT tool description"
        )
    
    async def _execute_async(self, *args, **kwargs) -> OSINTToolResult:
        # Implement custom logic
        return OSINTToolResult(
            success=True,
            data={"custom": "data"}
        )
```

### Creating Custom Workflows

```python
from workflows.base_workflow import BaseOSINTWorkflow

class CustomWorkflow(BaseOSINTWorkflow):
    def __init__(self):
        super().__init__("custom_workflow")
    
    def custom_analysis(self, target):
        # Define custom analysis logic
        pass
```

## 🚨 Limitations & Notes

1. **API Dependencies**: Full functionality requires API keys for external services
2. **Rate Limiting**: Respect API rate limits and terms of service
3. **Data Accuracy**: Always verify information from multiple sources
4. **Legal Compliance**: Ensure compliance with local laws and regulations
5. **Stagehand**: Currently a placeholder - requires manual installation from source

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **CrewAI**: Multi-agent framework
- **Serper.dev**: Google Search API
- **Crawl4AI**: Intelligent web crawling
- **Browser-Use**: Browser automation
- **OpenAI/Anthropic**: Language model APIs

---

**⚠️ Disclaimer**: This tool is for educational and legitimate research purposes only. Users are responsible for ensuring compliance with applicable laws and regulations when conducting OSINT activities.
