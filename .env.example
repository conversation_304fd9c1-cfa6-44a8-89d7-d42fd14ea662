# CrewAI OSINT Agent Configuration
# Copy this file to .env and fill in your API keys

# Language Model API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Search API Keys
SERPER_API_KEY=your_serper_api_key_here

# Model Configuration
DEFAULT_LLM_MODEL=gpt-4-turbo-preview
LLM_TEMPERATURE=0.1
MAX_TOKENS=4000

# Browser Configuration
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30

# Crawling Configuration
CRAWL_DELAY=1.0
MAX_PAGES_PER_DOMAIN=10
USER_AGENT=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36

# Output Configuration
OUTPUT_FORMAT=json
SAVE_RAW_DATA=true

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/osint_agent.log
